import { PathResolver } from './PathResolver'
import { VaultCoreService } from './VaultCoreService'
import { EventBus } from './EventBus'
import * as fs from 'fs'

export class IntelligenceCoreService {
  private vault: VaultCoreService

  constructor() {
    this.vault = new VaultCoreService()
  }

  async save(filePath: string, vaultPath: string, payload: { rawMarkdown?: string; json?: any }): Promise<{ success: boolean; error?: string }> {
    try {
      // V02 UNIFIED: Use files-based storage as the single location
      const { filesDir, jsonPath, fileHash } = PathResolver.getIntelligenceJsonPath(filePath, vaultPath)
      await fs.promises.mkdir(filesDir, { recursive: true })

      const now = new Date().toISOString()

      // KERNEL-ONLY: Only save JSON, no markdown fallbacks
      if (payload?.json && typeof payload.json === 'object') {
        const base = payload.json
        const canonical = {
          document_hash: base.document_hash || fileHash,
          file_path: base.file_path || PathResolver.normalizePath(filePath),
          analysis_timestamp: base.analysis_timestamp || now,
          labels: Array.isArray(base.labels) ? base.labels : [],
          summary: typeof base.summary === 'string' ? base.summary : '',
          key_points: Array.isArray(base.key_points) ? base.key_points : [],
          key_ideas: Array.isArray(base.key_ideas) ? base.key_ideas : [],
          weighted_entities: Array.isArray(base.weighted_entities) ? base.weighted_entities : [],
          human_connections: Array.isArray(base.human_connections) ? base.human_connections : [],
          processing_confidence: typeof base.processing_confidence === 'number' ? base.processing_confidence : 0,
          analysis_metadata: base.analysis_metadata || {},
          storage_metadata: {
            context_path: PathResolver.normalizePath(vaultPath),
            file_hash: fileHash,
            stored_at: now,
            storage_version: '2.0'
          },
          sessions: Array.isArray(base.sessions) ? base.sessions : [],
          smart_annotations: Array.isArray(base.smart_annotations) ? base.smart_annotations : [],
          created_at: base.created_at || now,
          updated_at: now
        }

        // V02 UNIFIED: Save to single file location
        await this.vault.writeTextFile(jsonPath, JSON.stringify(canonical, null, 2))

        // Notify listeners that canonical JSON is updated
        EventBus.broadcast('intelligence:updated', {
          filePath: PathResolver.normalizePath(filePath),
          vaultPath: PathResolver.normalizePath(vaultPath),
          fileHash,
          ideaCount: Array.isArray(canonical.key_ideas) ? canonical.key_ideas.length : 0,
          updatedAt: now
        })
      }

      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }

  async get(filePath: string, vaultPath: string): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const { filesDir, jsonPath, fileHash } = PathResolver.getIntelligenceJsonPath(filePath, vaultPath)

      console.log('[CORE] 🔍 IntelligenceCoreService.get called')
      console.log('[CORE] 🔍 filePath:', filePath)
      console.log('[CORE] 🔍 vaultPath:', vaultPath)
      console.log('[CORE] 🔍 fileHash:', fileHash)
      console.log('[CORE] 🔍 jsonPath:', jsonPath)

      // V02 UNIFIED: Use single file location for both read and write
      try {
        const content = await this.vault.readTextFile(jsonPath)
        const data = JSON.parse(content)

        // CORRUPTION DETECTION: Verify this intelligence belongs to the requested file
        if (data.file_path && data.file_path !== PathResolver.normalizePath(filePath)) {
          console.log('[CORE] 🚨 CORRUPTION DETECTED: file_path mismatch')
          console.log('[CORE] 🚨 Expected:', PathResolver.normalizePath(filePath))
          console.log('[CORE] 🚨 Found:', data.file_path)
          console.log('[CORE] 🚨 Returning null to prevent data corruption')
          return { success: true, data: null }
        }

        // VALIDATION: Ensure intelligence has actual content
        if (!data.key_ideas || data.key_ideas.length === 0) {
          console.log('[CORE] ⚠️ Intelligence found but has no key_ideas, returning null')
          return { success: true, data: null }
        }

        console.log('[CORE] ✅ Valid intelligence loaded with', data.key_ideas.length, 'ideas')
        return { success: true, data }
      } catch (error) {
        // File doesn't exist or is invalid - this is expected for new files
        console.log('[CORE] ℹ️ No intelligence JSON found for file:', filePath)
        console.log('[CORE] ℹ️ This is normal for files that haven\'t been analyzed yet')
        return { success: true, data: null }
      }
    } catch (error: any) {
      console.error('[CORE] ❌ Error in IntelligenceCoreService.get:', error)
      return { success: false, error: error.message }
    }
  }

  // KERNEL-ONLY: No markdown fallbacks needed
  // async parseMarkdown(filePath: string, vaultPath: string): Promise<{ success: boolean; data?: any; error?: string }> {
  //   // This method is no longer needed in kernel-only approach
  //   return { success: false, error: 'Markdown parsing not supported in kernel-only mode' }
  // }

  // V02 UNIFIED: List sessions from single file location (not separate sessions directory)
  async listSessions(filePath: string, vaultPath: string): Promise<{ success: boolean; sessions?: any[]; error?: string }> {
    try {
      // Use the same location as intelligence:read/write
      const { jsonPath } = PathResolver.getIntelligenceJsonPath(filePath, vaultPath)

      try {
        const content = await this.vault.readTextFile(jsonPath)
        const data = JSON.parse(content)

        // Convert intelligence format to session format for compatibility
        const session = {
          id: `session_${Date.now()}`,
          key_ideas: data.key_ideas || [],
          summary: '',
          entities: data.weighted_entities || [],
          created_at: data.created_at || new Date().toISOString(),
          smart_annotations: data.smart_annotations || []
        }

        return { success: true, sessions: [session] }
      } catch (error) {
        // No file exists - return empty sessions
        return { success: true, sessions: [] }
      }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }

  // V02 UNIFIED: Write session to single file location (same as intelligence:write)
  async writeSession(filePath: string, vaultPath: string, session: any): Promise<{ success: boolean; storedAt?: string; error?: string }> {
    try {
      // Convert session format to intelligence format and use the same storage location
      const intelligenceData = {
        file_path: filePath,
        key_ideas: session.key_ideas || [],
        weighted_entities: session.entities || [],
        human_connections: [],
        processing_confidence: 0.85,
        analysis_metadata: {
          processing_time_ms: 0,
          model_used: 'session_write',
          timestamp: session.created_at || new Date().toISOString()
        },
        created_at: session.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
        smart_annotations: session.smart_annotations || []
      }

      // Use the same save method as intelligence:write
      const result = await this.save(filePath, vaultPath, { json: intelligenceData })

      if (result.success) {
        const { jsonPath } = PathResolver.getIntelligenceJsonPath(filePath, vaultPath)
        return { success: true, storedAt: jsonPath }
      } else {
        return { success: false, error: result.error }
      }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }
}
