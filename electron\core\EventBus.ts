import { BrowserWindow } from 'electron'

export type EventPayload = Record<string, any>

type SubscriptionCategory = 'file' | 'intelligence' | 'task'

interface Subscription {
  id: string
  category: SubscriptionCategory
  filter?: {
    pathPrefix?: string
    eventNames?: string[]
  }
}

export class EventBus {
  private static subscriptions: Map<string, Subscription> = new Map()
  private static debounceTimers: Map<string, NodeJS.Timeout> = new Map()
  private static lastPayloads: Map<string, EventPayload> = new Map()
  private static readonly debounceMs = 100

  static broadcast(eventName: string, payload: EventPayload): void {
    for (const win of BrowserWindow.getAllWindows()) {
      win.webContents.send(`events:${eventName}`, payload)
    }
    // Also deliver to filtered subscribers
    this.deliverToSubscribers(eventName, payload)
  }

  static subscribe(category: SubscriptionCategory, filter?: Subscription['filter']): { subscriptionId: string } {
    const id = `sub_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`
    this.subscriptions.set(id, { id, category, filter })
    return { subscriptionId: id }
  }

  static unsubscribe(subscriptionId: string): { success: boolean } {
    this.subscriptions.delete(subscriptionId)
    return { success: true }
  }

  private static deliverToSubscribers(eventName: string, payload: EventPayload): void {
    const category = eventName.split(':')[0] as SubscriptionCategory
    for (const [id, sub] of this.subscriptions.entries()) {
      if (sub.category !== category) continue
      if (!this.matchesFilter(sub.filter, eventName, payload)) continue

      const key = `${id}:${eventName}`
      this.lastPayloads.set(key, payload)

      // Debounce/coalesce
      const existing = this.debounceTimers.get(key)
      if (existing) {
        clearTimeout(existing)
      }
      const timer = setTimeout(() => {
        const last = this.lastPayloads.get(key) || payload
        for (const win of BrowserWindow.getAllWindows()) {
          win.webContents.send(`events:subscription:${id}`, { eventName, payload: last })
        }
        this.debounceTimers.delete(key)
        this.lastPayloads.delete(key)
      }, this.debounceMs)
      this.debounceTimers.set(key, timer)
    }
  }

  private static matchesFilter(filter: Subscription['filter'] | undefined, eventName: string, payload: EventPayload): boolean {
    if (!filter) return true
    if (filter.eventNames && filter.eventNames.length > 0) {
      if (!filter.eventNames.includes(eventName)) return false
    }
    if (filter.pathPrefix && typeof payload?.path === 'string') {
      const normalizedPrefix = String(filter.pathPrefix).replace(/\\/g, '/').toLowerCase()
      const normalizedPath = String(payload.path).replace(/\\/g, '/').toLowerCase()
      if (!normalizedPath.startsWith(normalizedPrefix)) return false
    }
    return true
  }
}
