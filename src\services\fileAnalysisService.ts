/**
 * File Analysis Service
 * Specialized service for AI-powered document analysis using local models
 * Focuses on extracting 10+ key ideas with relevance scores and human connections
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { localModelService } from './localModelService'
import { llmResponseParser } from './llmResponseParser'
import {
  KeyIdea,
  WeightedEntity,
  HumanConnection,
  FileIntelligence,
  FileIntelligenceConfig,
  IntentType,
  DEFAULT_FILE_INTELLIGENCE_CONFIG,
  PRIORITY_WEIGHTS
} from '../types/fileIntelligenceTypes'
import { intelligence as intelligenceClient } from '../api/UnifiedAPIClient'

export interface DocumentAnalysisResult {
  key_ideas: KeyIdea[]
  weighted_entities: WeightedEntity[]
  human_connections: HumanConnection[]
  processing_confidence: number
  analysis_metadata: {
    model_used: string
    processing_time_ms: number
    content_length: number
    fallback_used: boolean
  }
}

class FileAnalysisService extends BaseService {
  private _lastRawMarkdown: string | null = null
  constructor() {
    super({
      name: 'FileAnalysisService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    this.logger.info('File Analysis Service initialized', 'doInitialize')
  }

  /**
   * Main analysis method: Extract comprehensive intelligence from document content
   */
  async analyzeDocument(
    content: string,
    config: Partial<FileIntelligenceConfig> = {}
  ): Promise<DocumentAnalysisResult> {
    const fullConfig = { ...DEFAULT_FILE_INTELLIGENCE_CONFIG, ...config }
    const startTime = Date.now()

    return await this.executeOperationOrThrow(
      'analyzeDocument',
      async () => {
        this.logger.info('Starting document analysis', 'analyzeDocument', {
          contentLength: content.length,
          model: fullConfig.local_model_preferred,
          minIdeas: fullConfig.min_ideas_required
        })

        let keyIdeas: KeyIdea[] = []
        let fallbackUsed = false
        let modelUsed = fullConfig.local_model_preferred

        try {
          // Try local model analysis first
          console.log('[LABELS] 🤖 Attempting local model analysis...')
          keyIdeas = await this.extractKeyIdeasWithLocalModel(content, fullConfig)
          console.log('[LABELS] 🤖 ✅ Local model analysis successful, extracted', keyIdeas.length, 'ideas')
        } catch (error: any) {
          console.log('[LABELS] 🤖 ❌ Local model analysis failed:', error?.message)
          this.logger.warn('Local model analysis failed, using fallback', 'analyzeDocument', error as Error)

          if (fullConfig.fallback_to_keyword_extraction) {
            console.log('[LABELS] 🤖 🔄 Using keyword-based fallback...')
            keyIdeas = await this.extractKeyIdeasKeywordBased(content, fullConfig)
            fallbackUsed = true
            modelUsed = 'keyword_fallback'
            console.log('[LABELS] 🤖 ✅ Keyword fallback successful, extracted', keyIdeas.length, 'ideas')
          } else {
            console.log('[LABELS] 🤖 ❌ Fallback disabled, throwing error')
            throw error
          }
        }

        // Extract weighted entities with human connection priority
        const weightedEntities = await this.extractWeightedEntities(content, keyIdeas)

        // Extract human connections (highest priority)
        const humanConnections = await this.extractHumanConnections(content, weightedEntities)

        // Calculate overall confidence
        const processingConfidence = this.calculateOverallConfidence(keyIdeas, weightedEntities)

        const processingTime = Date.now() - startTime

        const result: DocumentAnalysisResult = {
          key_ideas: keyIdeas,
          weighted_entities: weightedEntities,
          human_connections: humanConnections,
          processing_confidence: processingConfidence,
          analysis_metadata: {
            model_used: modelUsed,
            processing_time_ms: processingTime,
            content_length: content.length,
            fallback_used: fallbackUsed
          }
        }

        this.logger.info('Document analysis completed', 'analyzeDocument', {
          ideasExtracted: keyIdeas.length,
          entitiesFound: weightedEntities.length,
          humanConnections: humanConnections.length,
          confidence: processingConfidence,
          processingTime
        })

        return result
      },
      { contentLength: content.length }
    )
  }

  /**
   * Extract key ideas using local model with enhanced prompting
   */
  private async extractKeyIdeasWithLocalModel(
    content: string,
    config: FileIntelligenceConfig
  ): Promise<KeyIdea[]> {
    // SYSTEMATIC CHECK: Service and Model Availability
    console.log('[LABELS] 🔍 ===== SYSTEMATIC SERVICE CHECK =====')
    console.log('[LABELS] 🔍 1. Checking local model service health...')

    try {
      // Check service health first
      const providerStatus = await localModelService.getProviderStatus()
      console.log('[LABELS] 🔍 Provider Status:', {
        ollama: { connected: providerStatus.ollama.isConnected, models: providerStatus.ollama.models.length },
        lmstudio: { connected: providerStatus.lmstudio.isConnected, models: providerStatus.lmstudio.models.length }
      })

      // Check if any provider is connected
      const anyProviderConnected = providerStatus.ollama.isConnected || providerStatus.lmstudio.isConnected
      if (!anyProviderConnected) {
        console.log('[LABELS] 🔍 ❌ NO PROVIDERS CONNECTED!')
        console.log('[LABELS] 🔍 Ollama URL:', 'http://localhost:11434')
        console.log('[LABELS] 🔍 LM Studio URL:', 'http://localhost:1234')
        throw new ServiceError(
          ServiceErrorCode.EXTERNAL_SERVICE_ERROR,
          'No local model providers (Ollama/LM Studio) are available. Please start Ollama or LM Studio.',
          { serviceName: this.serviceName, operation: 'extractKeyIdeasWithLocalModel' }
        )
      }

    } catch (error) {
      const err = error as Error
      console.log('[LABELS] 🔍 ❌ Service health check failed:', err.message)
      throw error
    }

    console.log('[LABELS] 🔍 2. Checking model availability...')
    console.log('[LABELS] 🔍 Requested model:', config.local_model_preferred)

    const localModels = await localModelService.getAllLocalModels()
    console.log('[LABELS] 🔍 Available models:', localModels.map(m => ({ id: m.id, name: m.name, provider: m.provider })))

    const preferredModel = localModels.find(m => m.id === config.local_model_preferred)

    if (!preferredModel) {
      console.log('[LABELS] 🔍 ❌ REQUESTED MODEL NOT FOUND!')
      console.log('[LABELS] 🔍 Requested:', config.local_model_preferred)
      console.log('[LABELS] 🔍 Available:', localModels.map(m => m.id))

      // Try to suggest alternatives
      const alternatives = localModels.filter(m => m.id.includes('gemma') || m.id.includes('llama'))
      if (alternatives.length > 0) {
        console.log('[LABELS] 🔍 💡 Suggested alternatives:', alternatives.map(m => m.id))
      }

      throw new ServiceError(
        ServiceErrorCode.EXTERNAL_SERVICE_ERROR,
        `Requested model '${config.local_model_preferred}' not available. Available models: ${localModels.map(m => m.id).join(', ')}`,
        { serviceName: this.serviceName, operation: 'extractKeyIdeasWithLocalModel' }
      )
    }

    console.log('[LABELS] 🔍 ✅ Model found:', preferredModel.name)
    console.log('[LABELS] 🔍 ===== SERVICE CHECK COMPLETE =====')
    console.log('[LABELS] 🤖 Using model:', preferredModel.name)

    // Build enhanced prompt for key idea extraction
    const prompt = this.buildEnhancedKeyIdeaPrompt(content, config.min_ideas_required)
    const messages = [{ role: 'user', content: prompt }]

    console.log('[LABELS] 🤖 📡 Sending request to local model:', config.local_model_preferred)
    console.log('[LABELS] 🤖 📡 Prompt preview:', prompt.substring(0, 300) + '...')

    // Send to local model
    const response = await localModelService.sendMessage(
      config.local_model_preferred,
      messages
    )

    // DETAILED LLM RESPONSE ANALYSIS
    console.log('[LABELS] 🤖 📝 ===== RAW LLM RESPONSE ANALYSIS =====')
    console.log('[LABELS] 🤖 📝 Response type:', typeof response)
    console.log('[LABELS] 🤖 📝 Response length:', typeof response === 'string' ? response.length : 'N/A')

    if (typeof response === 'string') {
      console.log('[LABELS] 🤖 📝 FULL RESPONSE:')
      console.log('---START---')
      console.log(response)
      console.log('---END---')

      // DEBUG: Write raw response to file for inspection
      try {
        const debugPath = 'C:\\Users\\<USER>\\Documents\\Test20\\debug_llm_response.txt'
        if (window.electronAPI?.vault?.writeFile) {
          await window.electronAPI.vault.writeFile(debugPath, `LLM Response Debug - ${new Date().toISOString()}\n\nResponse Length: ${response.length}\n\n${response}`)
          console.log('[LABELS] 🤖 📝 DEBUG: Raw response written to:', debugPath)
        }
      } catch (e) {
        console.log('[LABELS] 🤖 📝 DEBUG: Failed to write debug file:', e)
      }

      // Check what format the response is in
      const hasFileIntelBlock = response.includes('!-- FILE_INTEL:BEGIN --')
      const hasJsonFence = response.includes('```json') || response.includes('```')
      const hasJsonObject = response.includes('{') && response.includes('}')
      const hasKeyIdeas = response.toLowerCase().includes('key_ideas')

      console.log('[LABELS] 🤖 📝 Format analysis:', {
        hasFileIntelBlock,
        hasJsonFence,
        hasJsonObject,
        hasKeyIdeas,
        isEmpty: response.trim().length === 0
      })

      // Show first few lines to understand the format
      const lines = response.split('\n').slice(0, 10)
      console.log('[LABELS] 🤖 📝 First 10 lines:')
      lines.forEach((line, i) => {
        console.log(`[LABELS] 🤖 📝 Line ${i + 1}: "${line}"`)
      })

      // Capture raw markdown block for dual storage
      this._lastRawMarkdown = response
    } else {
      console.log('[LABELS] 🤖 📝 ❌ Response is not a string! Actual value:', response)
    }
    console.log('[LABELS] 🤖 📝 ===== END RESPONSE ANALYSIS =====')

    this.logger.info('Received LLM response', 'extractKeyIdeasWithLocalModel', {
      length: typeof response === 'string' ? response.length : 0,
      responseType: typeof response
    })

    // Parse and validate response
    return this.parseAndValidateKeyIdeas(response, config)
  }

  /**
   * Generate consistent hash for content to ensure reproducible results
   * Uses simple string hashing for browser compatibility
   */
  private generateContentHash(content: string, config: FileIntelligenceConfig): string {
    const hashInput = JSON.stringify({
      content: content.substring(0, 8000), // Use same truncation as prompt
      model: config.local_model_preferred,
      minIdeas: config.min_ideas_required,
      temperature: config.temperature,
      version: '1.0' // Increment when prompt changes
    })

    // Simple hash function for browser compatibility
    let hash = 0
    for (let i = 0; i < hashInput.length; i++) {
      const char = hashInput.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16).padStart(8, '0')
  }

  /**
   * Build FILE_INTEL prompt for local model keyword extraction (copied from fileIntelligenceService.ts)
   */
  private buildEnhancedKeyIdeaPrompt(content: string, minIdeas: number): string {

    return `You are a precise extractor. Do NOT explain. Output exactly ONE fenced Markdown block, nothing else.

Task
- Read the document between DOC and DOC.
- Produce 10 key ideas as 1–4 word noun phrases (no verbs, no punctuation).
- Be tolerant: if the doc is short, output as many as you can (≥3).
- Use only these intents: topic, knowledge, connection, action, reference.

Output format (strict)
!-- FILE_INTEL:BEGIN --
## Key Ideas (1–4 words each)
- text=noun-phrase; score=98; intents=topic,knowledge; context=short hint; entities=Foo,Bar
- text=noun-phrase; score=95; intents=knowledge,action; context=short hint; entities=A,B,C
- text=noun-phrase; score=92; intents=topic; context=short hint; entities=
!-- FILE_INTEL:END --

Rules
- Exactly one block, no prose outside it.
- Each line starts with “- ” and uses key=value pairs separated by “; ”.
- text: 1–4 words, letters/numbers/spaces only, Title Case preferred, wrap in quotes.
- score: integer 0–100.
- intents: comma-separated from {topic,knowledge,connection,action,reference}.
- context: ≤10 words, quoted.
- entities: 0+ comma-separated tokens; leave empty if none.
- If you can’t find 10, output fewer but keep format.
- Do not include code fences inside the block.

DOC
[PASTE YOUR DOCUMENT HERE]
DOC`
  }

  /**
   * Parse and validate key ideas from local model response
   */
  private parseAndValidateKeyIdeas(response: string, config: FileIntelligenceConfig): KeyIdea[] {
    console.log('[LABELS] 🤖 📝 Parsing AI model response...')
    console.log('[LABELS] 🤖 📝 Response length:', response.length)
    console.log('[LABELS] 🤖 📝 Response preview:', response.substring(0, 500))

    try {
      // Use the new LLM response parser instead of old extractJsonPayload
      console.log('[LABELS] 🤖 📝 Using NEW LLM Response Parser...')
      const parseResult = llmResponseParser.parseResponse(response)

      if (!parseResult.success) {
        console.log('[LABELS] 🤖 📝 ❌ NEW PARSER failed:', parseResult.error)
        console.log('[LABELS] 🤖 📝 ❌ Debug info:', parseResult.debug_info)
        throw new Error(parseResult.error || 'Failed to parse LLM response')
      }

      console.log('[LABELS] 🤖 📝 ✅ NEW PARSER successful! Extracted', parseResult.key_ideas.length, 'ideas')
      console.log('[LABELS] 🤖 📝 ✅ Debug info:', parseResult.debug_info)

      // Use the parsed ideas directly from the new parser
      const ideas = parseResult.key_ideas.map((idea, index) => {
        // Ensure compatibility with existing KeyIdea interface
        const relevanceScore = Math.min(100, Math.max(0, idea.relevance_score || 50))
        const intentTypes = Array.isArray(idea.intent_types) ? idea.intent_types : ['topic']

        return {
          ...idea,
          id: `ai_idea_${Date.now()}_${index}`,
          relevance_score: relevanceScore,
          intent_types: intentTypes as IntentType[],
          weight: this.calculateIdeaWeight(relevanceScore, intentTypes as IntentType[]),
          auto_selected: false, // Will be set later
          user_confirmed: false,
          extracted_from: 'local_model_ai'
        }
      })

      // Ensure minimum ideas requirement
      if (ideas.length < config.min_ideas_required) {
        this.logger.warn('Insufficient ideas extracted from AI model', 'parseAndValidateKeyIdeas', {
          extracted: ideas.length,
          required: config.min_ideas_required
        })
      }

      // Sort by relevance score and auto-select top N
      ideas.sort((a, b) => b.relevance_score - a.relevance_score)
      ideas.slice(0, config.auto_select_top_n).forEach(idea => {
        idea.auto_selected = true
      })

      const avgRel = ideas.length ? (ideas.reduce((sum, i) => sum + i.relevance_score, 0) / ideas.length) : 0
      this.logger.info('Successfully parsed key ideas from AI model', 'parseAndValidateKeyIdeas', {
        totalIdeas: ideas.length,
        autoSelected: ideas.filter(i => i.auto_selected).length,
        avgRelevance: avgRel
      })
      try {
        console.log('[LLM-Parse] ideas (top 10):', ideas.slice(0, 10).map(i => `${i.text} (${i.relevance_score})`))
      } catch {}

      return ideas

    } catch (error) {
      const err = error as Error
      console.log('[LABELS] 🤖 📝 ❌ Failed to parse AI response:', err.message)
      console.log('[LABELS] 🤖 📝 ❌ Error type:', err.constructor.name)
      this.logger.error('Failed to parse AI model response', 'parseAndValidateKeyIdeas', err)

      // Re-throw the original error so the fallback mechanism can catch it
      throw error
    }
  }

  /**
   * Robust JSON extraction from model responses with common LLM artifacts handled.
   */
  private extractJsonPayload(response: string): any {
    console.log('[LABELS] 🔄 ===== MARKDOWN → JSON CONVERSION =====')
    console.log('[LABELS] 🔄 Starting FILE_INTEL block extraction...')

    try {
      const startMarker = '!-- FILE_INTEL:BEGIN --'
      const endMarker = '!-- FILE_INTEL:END --'
      console.log('[LABELS] 🔄 Looking for markers:', { startMarker, endMarker })

      const startIdx = response.indexOf(startMarker)
      const endIdx = response.indexOf(endMarker, startIdx + startMarker.length)
      console.log('[LABELS] 🔄 Marker positions:', { startIdx, endIdx })

      if (startIdx !== -1 && endIdx !== -1 && endIdx > startIdx) {
        console.log('[LABELS] 🔄 ✅ FILE_INTEL block found!')
        let inner = response.substring(startIdx + startMarker.length, endIdx)
        console.log('[LABELS] 🔄 Raw inner content length:', inner.length)
        console.log('[LABELS] 🔄 Raw inner content preview:', inner.substring(0, 200))

        // Normalize and strip common artifacts
        inner = inner
          .replace(/```[a-zA-Z]*\n?/g, '')
          .replace(/```/g, '')
          .replace(/\r/g, '')
          .replace(/[“”]/g, '"')
          .replace(/[‘’]/g, "'")

        console.log('[LABELS] 🔄 Normalized content length:', inner.length)
        console.log('[LABELS] 🔄 Normalized content preview:', inner.substring(0, 200))

        const lines = inner.split('\n').map(l => l.trim()).filter(Boolean)
        console.log('[LABELS] 🔄 Total lines found:', lines.length)
        console.log('[LABELS] 🔄 First 5 lines:', lines.slice(0, 5))
        const key_ideas: any[] = []
        console.log('[LABELS] 🔄 Starting line-by-line parsing...')

        for (let i = 0; i < lines.length; i++) {
          const raw = lines[i]
          console.log('[LABELS] 🔄 Processing line', i + 1, ':', raw)

          if (!raw.startsWith('- ')) {
            console.log('[LABELS] 🔄 ⏭️ Skipping line (not starting with "- ")')
            continue
          }

          const body = raw.slice(2)
          console.log('[LABELS] 🔄 Line body:', body)

          const parts = body.split(';').map(p => p.trim()).filter(Boolean)
          console.log('[LABELS] 🔄 Split parts:', parts)

          const kv: Record<string, string> = {}

          // FIXED NEW LOGIC: Handle format like "Project Management ; score=99; intents=topic,action"
          // Check if first part doesn't contain "=" (meaning it's the text, not a key=value pair)
          if (parts.length > 0 && !parts[0].includes('=')) {
            console.log('[LABELS] 🔄 🔧 Detected NEW format with text first (no quotes, no =)')
            // First part is the text
            kv['text'] = parts[0].trim()
            console.log('[LABELS] 🔄 🔧 Extracted text from first part:', kv['text'])

            // FIXED: Join remaining parts and split by "; " to get individual key=value pairs
            const remainingParts = parts.slice(1).join(';')
            const keyValueParts = remainingParts.split(';')

            // Process key=value pairs
            for (const part of keyValueParts) {
              const trimmedPart = part.trim()
              const eq = trimmedPart.indexOf('=')
              if (eq <= 0) {
                console.log('[LABELS] 🔄 ⏭️ Skipping part (no = found):', trimmedPart)
                continue
              }
              const k = trimmedPart.slice(0, eq).trim().toLowerCase()
              let v = trimmedPart.slice(eq + 1).trim()
              if ((k === 'context') && /^"[\s\S]*"$/.test(v)) v = v.slice(1, -1)
              v = v.replace(/,+$/g, '')
              kv[k] = v
              console.log('[LABELS] 🔄 🔧 Extracted key-value:', { key: k, value: v })
            }
          }
          // Handle LLM format: - "text" ; key=value; key=value
          else if (parts.length > 0 && parts[0].startsWith('"') && parts[0].endsWith('"')) {
            console.log('[LABELS] 🔄 🔧 Detected LLM format with quoted text first')
            // Extract quoted text as the main text
            kv['text'] = parts[0].slice(1, -1) // Remove quotes
            console.log('[LABELS] 🔄 🔧 Extracted text from quotes:', kv['text'])

            // Process remaining parts as key=value pairs
            for (let i = 1; i < parts.length; i++) {
              const part = parts[i]
              const eq = part.indexOf('=')
              if (eq <= 0) {
                console.log('[LABELS] 🔄 ⏭️ Skipping part (no = found):', part)
                continue
              }
              const k = part.slice(0, eq).trim().toLowerCase()
              let v = part.slice(eq + 1).trim()
              if ((k === 'context') && /^"[\s\S]*"$/.test(v)) v = v.slice(1, -1)
              v = v.replace(/,+$/g, '')
              kv[k] = v
              console.log('[LABELS] 🔄 🔧 Extracted key-value:', { key: k, value: v })
            }
          } else {
            // Handle standard format: text=value; key=value
            console.log('[LABELS] 🔄 Using standard key=value parsing')
            for (const part of parts) {
              const eq = part.indexOf('=')
              if (eq <= 0) {
                console.log('[LABELS] 🔄 ⏭️ Skipping part (no = found):', part)
                continue
              }
              const k = part.slice(0, eq).trim().toLowerCase()
              let v = part.slice(eq + 1).trim()
              if ((k === 'text' || k === 'context') && /^"[\s\S]*"$/.test(v)) v = v.slice(1, -1)
              v = v.replace(/,+$/g, '')
              kv[k] = v
              console.log('[LABELS] 🔄 Extracted key-value:', { key: k, value: v })
            }
          }

          if (!kv['text']) {
            console.log('[LABELS] 🔄 ⏭️ Skipping line (no text field)')
            continue
          }

          const score = Math.max(0, Math.min(100, parseInt(kv['score'] || '50', 10) || 50))
          const intents = (kv['intents'] || '')
            .split(',')
            .map(s => s.trim())
            .filter(Boolean)
          const context = kv['context'] || ''
          const entities = (kv['entities'] || '')
            .split(',')
            .map(s => s.trim())
            .filter(Boolean)

          const ideaObject = {
            text: kv['text'],
            relevance_score: score,
            intent_types: intents.length ? intents : ['topic'],
            context,
            entities_mentioned: entities
          }

          console.log('[LABELS] 🔄 ✅ Created idea object:', ideaObject)
          key_ideas.push(ideaObject)
        }

        console.log('[LABELS] 🔄 ✅ FILE_INTEL parsing complete!')
        console.log('[LABELS] 🔄 Total ideas extracted:', key_ideas.length)
        console.log('[LABELS] 🔄 ===== CONVERSION SUCCESSFUL =====')

        return { key_ideas }
      } else {
        console.log('[LABELS] 🔄 ❌ FILE_INTEL markers not found')
        console.log('[LABELS] 🔄 Response contains "!-- FILE_INTEL:BEGIN --":', response.includes('!-- FILE_INTEL:BEGIN --'))
        console.log('[LABELS] 🔄 Response contains "!-- FILE_INTEL:END --":', response.includes('!-- FILE_INTEL:END --'))
      }
    } catch (e) {
      const error = e as Error
      console.log('[LABELS] 🔄 ❌ FILE_INTEL parsing failed:', error.message)
      console.log('[LABELS] 🔄 ❌ Error details:', error)
      this.logger.warn('Index-based FILE_INTEL parsing failed, trying fallbacks', 'extractJsonPayload')
    }
    try { console.log('[LLM-Parse] FILE_INTEL block not found; trying JSON fallbacks'); } catch {}

    // Optional fallback: any fenced JSON block → parse and normalize
    console.log('[LABELS] 🤖 📝 Looking for JSON fences in response...')
    let fenceJson = response.match(/```json\s*([\s\S]*?)\s*```/i) || response.match(/```\s*([\s\S]*?)\s*```/)
    console.log('[LABELS] 🤖 📝 JSON fence match result:', fenceJson ? 'FOUND' : 'NOT FOUND')

    // Fallback: Handle incomplete JSON fence (missing closing ```)
    if (!fenceJson && response.startsWith('```json')) {
      console.log('[LABELS] 🤖 📝 🔧 Attempting incomplete fence repair...')
      const jsonContent = response.substring(7).trim() // Remove ```json
      fenceJson = ['', jsonContent] // Fake match array format
      console.log('[LABELS] 🤖 📝 🔧 Repaired fence, content length:', jsonContent.length)
    }

    if (fenceJson && fenceJson[1]) {
      console.log('[LABELS] 🤖 📝 ✅ JSON fence found, content length:', fenceJson[1].length)
      try {
        console.log('[LABELS] 🤖 📝 Found JSON fence, attempting to parse...')
        const fixed = this.sanitizeJsonString(fenceJson[1])
        console.log('[LABELS] 🤖 📝 Sanitized JSON:', fixed.substring(0, 300) + '...')

        const obj = JSON.parse(fixed)
        console.log('[LABELS] 🤖 📝 Parsed JSON successfully, type:', Array.isArray(obj) ? 'array' : typeof obj)
        console.log('[LABELS] 🤖 📝 Object keys:', Array.isArray(obj) ? `array[${obj.length}]` : Object.keys(obj))

        // Case 1: Already in expected shape
        if (obj && Array.isArray(obj.key_ideas)) {
          console.log('[LABELS] 🤖 📝 ✅ Case 1: Found key_ideas array')
          return obj
        }
        // Case 2: Model returned a plain array of ideas
        if (Array.isArray(obj)) {
          console.log('[LABELS] 🤖 📝 ✅ Case 2: Processing plain array of', obj.length, 'items')
          console.log('[LABELS] 🤖 📝 First item structure:', Object.keys(obj[0] || {}))
          console.log('[LABELS] 🤖 📝 First item:', obj[0])
          const key_ideas = obj.map((it: any, index: number) => {
            console.log(`[LABELS] 🤖 📝 Processing item ${index}:`, it)

            const intents = Array.isArray(it.intents)
              ? it.intents
              : (typeof it.intents === 'string' ? it.intents.split(',').map((s: string) => s.trim()).filter(Boolean) : [])
            const entities = Array.isArray(it.entities)
              ? it.entities
              : (typeof it.entities === 'string' ? it.entities.split(',').map((s: string) => s.trim()).filter(Boolean) : [])
            // Handle both 'score' and 'relevance_score' fields
            const scoreValue = it.score || it.relevance_score || 50
            const score = typeof scoreValue === 'number' ? scoreValue : parseInt(String(scoreValue), 10)

            const processed = {
              text: String(it.text || '').trim(),
              relevance_score: Math.max(0, Math.min(100, isNaN(score) ? 50 : score)),
              intent_types: intents.length ? intents : ['topic'],
              context: String(it.context || ''),
              entities_mentioned: entities
            }

            console.log(`[LABELS] 🤖 📝 Processed item ${index}:`, processed)
            return processed
          })

          console.log('[LABELS] 🤖 📝 ✅ Successfully converted array to key_ideas format')
          console.log('[LABELS] 🤖 📝 Final key_ideas count:', key_ideas.length)
          return { key_ideas }
        }
      } catch (e) {
        const error = e as Error
        console.log('[LABELS] 🤖 📝 ❌ Fenced JSON parsing failed:', error.message)
        console.log('[LABELS] 🤖 📝 ❌ Error details:', error)
        console.log('[LABELS] 🤖 📝 ❌ Raw fence content:', fenceJson[1].substring(0, 500))
        this.logger.warn('Fenced JSON parsing failed', 'extractJsonPayload')
      }
    } else {
      console.log('[LABELS] 🤖 📝 ❌ No JSON fence found in response')
      console.log('[LABELS] 🤖 📝 ❌ Response contains ```json:', response.includes('```json'))
      console.log('[LABELS] 🤖 📝 ❌ Response contains ```:', response.includes('```'))
      console.log('[LABELS] 🤖 📝 ❌ Response starts with ```json:', response.startsWith('```json'))
      console.log('[LABELS] 🤖 📝 ❌ Response ends with ```:', response.endsWith('```'))
      console.log('[LABELS] 🤖 📝 ❌ Response last 100 chars:', response.substring(response.length - 100))
      console.log('[LABELS] 🤖 📝 ❌ Response first 500 chars:', response.substring(0, 500))
    }
    // Do NOT attempt greedy { ... } matching across entire response; avoid parsing DOC content.
    console.log('[LABELS] 🤖 📝 ❌ Throwing "No file intel block or JSON found" error')
    throw new Error('No file intel block or JSON found in response')
  }

  /**
   * Fix common JSON issues produced by LLMs.
   */
  private sanitizeJsonString(input: string): string {
    let s = input.trim()
    // Remove leading/trailing backticks
    s = s.replace(/^```[a-zA-Z]*\n?/, '').replace(/```$/, '')
    // Replace smart quotes with ASCII quotes
    s = s.replace(/[“”]/g, '"').replace(/[‘’]/g, "'")
    // Remove trailing commas before } or ]
    s = s.replace(/,\s*(\}|\])/g, '$1')
    // Remove comments (// or /* */)
    s = s.replace(/\/\*[\s\S]*?\*\//g, '').replace(/(^|\n)\s*\/\/.*(?=\n|$)/g, '')
    // Ensure intent_types is array when provided as string
    s = s.replace(/"intent_types"\s*:\s*"([^"]+)"/g, '"intent_types": ["$1"]')
    return s
  }

  /**
   * Calculate idea weight based on relevance score and intent types
   */
  private calculateIdeaWeight(relevanceScore: number, intentTypes: IntentType[]): number {
    let baseWeight = relevanceScore / 100
    
    // Boost weight for human connection intents
    if (intentTypes.includes('connection')) {
      baseWeight *= 1.2
    }
    
    // Boost weight for action items
    if (intentTypes.includes('action')) {
      baseWeight *= 1.1
    }
    
    return Math.min(1.0, baseWeight)
  }

  /**
   * Fallback keyword-based extraction when AI model fails
   */
  private async extractKeyIdeasKeywordBased(content: string, config: FileIntelligenceConfig): Promise<KeyIdea[]> {
    this.logger.info('Using keyword-based fallback extraction', 'extractKeyIdeasKeywordBased')
    
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20)
    const ideas: KeyIdea[] = []

    // Enhanced keyword categories
    const keywordCategories = {
      high_priority: ['requirement', 'specification', 'design', 'architecture', 'security', 'performance'],
      medium_priority: ['feature', 'component', 'system', 'interface', 'integration', 'testing'],
      low_priority: ['data', 'user', 'deployment', 'api', 'database', 'documentation']
    }

    sentences.forEach((sentence, index) => {
      let relevanceScore = 20 // Base score
      let intentTypes: IntentType[] = ['topic']
      
      // Calculate relevance based on keyword presence
      Object.entries(keywordCategories).forEach(([priority, keywords]) => {
        const matches = keywords.filter(keyword => 
          sentence.toLowerCase().includes(keyword)
        ).length
        
        if (matches > 0) {
          switch (priority) {
            case 'high_priority':
              relevanceScore += matches * 25
              break
            case 'medium_priority':
              relevanceScore += matches * 15
              break
            case 'low_priority':
              relevanceScore += matches * 10
              break
          }
        }
      })

      // Check for human connection indicators
      if (/\b[A-Z][a-z]+ [A-Z][a-z]+\b/.test(sentence) || /@/.test(sentence)) {
        intentTypes.push('connection')
        relevanceScore += 20
      }

      if (relevanceScore > 40 && ideas.length < config.min_ideas_required * 2) {
        ideas.push({
          id: `keyword_idea_${index}`,
          text: sentence.trim(),
          relevance_score: Math.min(85, relevanceScore),
          intent_types: intentTypes,
          weight: this.calculateIdeaWeight(relevanceScore, intentTypes),
          auto_selected: ideas.length < config.auto_select_top_n,
          user_confirmed: false,
          context: 'keyword_extraction',
          extracted_from: 'keyword_fallback'
        })
      }
    })

    // Ensure minimum requirement with lower-quality ideas if needed
    while (ideas.length < config.min_ideas_required && sentences.length > ideas.length) {
      const sentence = sentences[ideas.length]
      ideas.push({
        id: `fallback_idea_${ideas.length}`,
        text: sentence.trim(),
        relevance_score: 35,
        intent_types: ['topic'],
        weight: 0.35,
        auto_selected: false,
        user_confirmed: false,
        context: 'fallback_extraction',
        extracted_from: 'keyword_fallback'
      })
    }

    return ideas.slice(0, Math.max(config.min_ideas_required, 20))
  }

  /**
   * Extract weighted entities with priority classification
   */
  private async extractWeightedEntities(content: string, keyIdeas: KeyIdea[]): Promise<WeightedEntity[]> {
    const entities: WeightedEntity[] = []

    // Extract people names (high priority)
    const namePattern = /\b[A-Z][a-z]+ [A-Z][a-z]+(?:\s[A-Z][a-z]+)?\b/g
    const names = [...new Set(content.match(namePattern) || [])]
    names.forEach(name => {
      entities.push({
        text: name,
        type: 'person',
        confidence: 0.8,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Name pattern detection',
        priority_level: 'high'
      })
    })

    // Extract email addresses (high priority)
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g
    const emails = [...new Set(content.match(emailPattern) || [])]
    emails.forEach(email => {
      entities.push({
        text: email,
        type: 'email',
        confidence: 0.95,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Email pattern detection',
        priority_level: 'high'
      })
    })

    // Extract company names (high priority)
    const companyPatterns = [
      /\b[A-Z][a-z]+ (?:Inc|Corp|LLC|Ltd|Company|Technologies|Systems|Solutions|Group|Enterprises)\b/g,
      /\b[A-Z][A-Z]+ (?:Inc|Corp|LLC|Ltd)\b/g
    ]

    const companies: string[] = []
    companyPatterns.forEach(pattern => {
      const matches = content.match(pattern) || []
      companies.push(...matches)
    })

    Array.from(new Set(companies)).forEach(company => {
      entities.push({
        text: company,
        type: 'company',
        confidence: 0.75,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Company pattern detection',
        priority_level: 'high'
      })
    })

    // Extract job titles (high priority)
    const titlePatterns = [
      /\b(?:Senior|Lead|Principal|Chief|Head of|Director of|Manager of|VP of)\s+[A-Z][a-z]+(?:\s[A-Z][a-z]+)*\b/g,
      /\b(?:CEO|CTO|CFO|COO|VP|SVP|EVP|President|Manager|Director|Engineer|Developer|Analyst|Consultant)\b/g
    ]

    const titles: string[] = []
    titlePatterns.forEach(pattern => {
      const matches = content.match(pattern) || []
      titles.push(...matches)
    })

    Array.from(new Set(titles)).forEach(title => {
      entities.push({
        text: title,
        type: 'title',
        confidence: 0.7,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Title pattern detection',
        priority_level: 'high'
      })
    })

    // Extract technical concepts (medium priority)
    const techKeywords = [
      'API', 'database', 'architecture', 'framework', 'algorithm', 'protocol',
      'interface', 'microservice', 'authentication', 'authorization', 'encryption',
      'deployment', 'integration', 'scalability', 'performance', 'security'
    ]

    techKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi')
      const matches = content.match(regex)
      if (matches && matches.length > 0) {
        entities.push({
          text: keyword,
          type: 'technical_concept',
          confidence: 0.6,
          weight: PRIORITY_WEIGHTS.medium,
          intent_types: ['knowledge', 'topic'],
          context: `Technical keyword (${matches.length} occurrences)`,
          priority_level: 'medium'
        })
      }
    })

    return entities
  }

  /**
   * Extract human connections from entities and content
   */
  private async extractHumanConnections(content: string, entities: WeightedEntity[]): Promise<HumanConnection[]> {
    const connections: HumanConnection[] = []

    const people = entities.filter(e => e.type === 'person')
    const emails = entities.filter(e => e.type === 'email')
    const companies = entities.filter(e => e.type === 'company')
    const titles = entities.filter(e => e.type === 'title')

    people.forEach(person => {
      const connection: HumanConnection = {
        name: person.text,
        connection_strength: person.confidence,
        collaboration_context: this.extractCollaborationContext(content, person.text),
        document_mentions: this.countMentions(content, person.text),
        priority_weight: 1.0
      }

      // Try to find associated email (look for email near the person's name)
      const personIndex = content.indexOf(person.text)
      if (personIndex !== -1) {
        const contextWindow = content.substring(
          Math.max(0, personIndex - 200),
          Math.min(content.length, personIndex + 200)
        )

        const associatedEmail = emails.find(e => contextWindow.includes(e.text))
        if (associatedEmail) {
          connection.email = associatedEmail.text
        }

        // Try to find associated company
        const associatedCompany = companies.find(c => contextWindow.includes(c.text))
        if (associatedCompany) {
          connection.company = associatedCompany.text
        }

        // Try to find associated title
        const associatedTitle = titles.find(t => contextWindow.includes(t.text))
        if (associatedTitle) {
          connection.title = associatedTitle.text
        }
      }

      connections.push(connection)
    })

    return connections
  }

  /**
   * Extract collaboration context for a person
   */
  private extractCollaborationContext(content: string, personName: string): string {
    const personIndex = content.indexOf(personName)
    if (personIndex === -1) return 'Mentioned in document'

    // Extract sentence containing the person's name
    const start = Math.max(0, content.lastIndexOf('.', personIndex))
    const end = content.indexOf('.', personIndex + personName.length)

    if (end !== -1) {
      return content.substring(start + 1, end).trim()
    }

    return 'Mentioned in document'
  }

  /**
   * Count mentions of a term in content
   */
  private countMentions(content: string, term: string): number {
    const regex = new RegExp(term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi')
    const matches = content.match(regex)
    return matches ? matches.length : 0
  }

  /**
   * Calculate overall confidence score
   */
  private calculateOverallConfidence(keyIdeas: KeyIdea[], entities: WeightedEntity[]): number {
    if (keyIdeas.length === 0) return 0

    const avgIdeaScore = keyIdeas.reduce((sum, idea) => sum + idea.relevance_score, 0) / keyIdeas.length
    const avgEntityConfidence = entities.length > 0
      ? entities.reduce((sum, entity) => sum + entity.confidence, 0) / entities.length
      : 0.5

    // Weight human connections higher in confidence calculation
    const humanConnectionBonus = entities.filter(e => e.priority_level === 'high').length * 0.05

    return Math.min(1.0, (avgIdeaScore / 100 + avgEntityConfidence + humanConnectionBonus) / 2)
  }

  /**
   * Analyze document and store intelligence data
   */
  async analyzeAndStoreDocument(
    content: string,
    filePath: string,
    vaultPath: string,
    config: Partial<FileIntelligenceConfig> = {}
  ): Promise<DocumentAnalysisResult> {
    return await this.executeOperationOrThrow(
      'analyzeAndStoreDocument',
      async () => {
        console.log('[LABELS] 🤖 analyzeAndStoreDocument called')
        console.log('[LABELS] 🤖 filePath:', filePath)
        console.log('[LABELS] 🤖 vaultPath:', vaultPath)
        console.log('[LABELS] 🤖 content length:', content.length)

        this.logger.info('Analyzing and storing document', 'analyzeAndStoreDocument', { filePath })

        // Perform analysis with robust fallback even if analyzeDocument throws
        let analysisResult: DocumentAnalysisResult
        try {
          console.log('[LABELS] 🤖 Starting document analysis...')
          analysisResult = await this.analyzeDocument(content, config)
          console.log('[LABELS] 🤖 ✅ Document analysis completed successfully')
        } catch (primaryError: any) {
          console.log('[LABELS] 🤖 ❌ Primary analysis failed, using keyword fallback')
          this.logger.warn('Primary analysis failed, forcing keyword fallback', 'analyzeAndStoreDocument', primaryError)
          const fullConfig = { ...DEFAULT_FILE_INTELLIGENCE_CONFIG, ...config }
          // Build minimal result using keyword fallback
          const keyIdeas = await this.extractKeyIdeasKeywordBased(content, fullConfig)
          const weightedEntities = await this.extractWeightedEntities(content, keyIdeas)
          const humanConnections = await this.extractHumanConnections(content, weightedEntities)
          const processingConfidence = this.calculateOverallConfidence(keyIdeas, weightedEntities)
          analysisResult = {
            key_ideas: keyIdeas,
            weighted_entities: weightedEntities,
            human_connections: humanConnections,
            processing_confidence: processingConfidence,
            analysis_metadata: {
              model_used: 'keyword_fallback',
              processing_time_ms: 0,
              content_length: content.length,
              fallback_used: true
            }
          }
        }

        // Create file intelligence object
        // Format timestamp in user-friendly format
        const formatTimestamp = () => {
          const date = new Date()
          return date.toLocaleString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
          }).replace(/(\d+)\/(\d+)\/(\d+),\s(\d+):(\d+):(\d+)/, '$3-$1-$2 $4:$5:$6')
        }

        const fileIntelligence: FileIntelligence = {
          file_path: filePath,
          key_ideas: analysisResult.key_ideas,
          weighted_entities: analysisResult.weighted_entities,
          human_connections: analysisResult.human_connections,
          processing_confidence: analysisResult.processing_confidence,
          analysis_metadata: {
            ...analysisResult.analysis_metadata,
            timestamp: formatTimestamp()
          },
          created_at: formatTimestamp(),
          updated_at: formatTimestamp()
        }

        // Store file intelligence
        console.log('[LABELS] 🤖 Storing intelligence data...')
        console.log('[LABELS] 🤖 fileIntelligence.file_path:', fileIntelligence.file_path)
        console.log('[LABELS] 🤖 fileIntelligence.key_ideas.length:', fileIntelligence.key_ideas.length)

        // await storageServiceAdapter.storeFileIntelligence(
        //   filePath,
        //   fileIntelligence,
        //   { vaultPath, createDirectories: true, rawMarkdown: this._lastRawMarkdown || undefined }
        // )
        await intelligenceClient.write(filePath, vaultPath, {
          json: {
            file_path: fileIntelligence.file_path,
            key_ideas: fileIntelligence.key_ideas,
            weighted_entities: fileIntelligence.weighted_entities,
            human_connections: fileIntelligence.human_connections,
            processing_confidence: fileIntelligence.processing_confidence,
            analysis_metadata: fileIntelligence.analysis_metadata,
            created_at: fileIntelligence.created_at,
            updated_at: fileIntelligence.updated_at
          },
          rawMarkdown: this._lastRawMarkdown || undefined
        })
        console.log('[LABELS] 🤖 ✅ Intelligence data stored successfully')

        // Update vault intelligence incrementally
        console.log('[LABELS] 🤖 Updating vault intelligence...')
        // TODO: Implement vault intelligence update in new architecture
        console.log('[LABELS] 🤖 ⚠️ Vault intelligence update temporarily disabled during migration')
        console.log('[LABELS] 🤖 ✅ Vault intelligence updated')

        this.logger.info('Document analyzed and stored successfully', 'analyzeAndStoreDocument', {
          filePath,
          ideasExtracted: analysisResult.key_ideas.length,
          entitiesFound: analysisResult.weighted_entities.length,
          humanConnections: analysisResult.human_connections.length
        })

        // Emit performance event for monitoring
        if (typeof window !== 'undefined' && window.dispatchEvent) {
          const fileName = filePath.split('/').pop() || filePath
          window.dispatchEvent(new CustomEvent('file-intelligence-complete', {
            detail: {
              processingTime: analysisResult.analysis_metadata.processing_time_ms,
              fileName,
              ideasExtracted: analysisResult.key_ideas.length,
              entitiesFound: analysisResult.weighted_entities.length,
              humanConnections: analysisResult.human_connections.length,
              confidence: analysisResult.processing_confidence
            }
          }))
        }

        return analysisResult
      },
      { filePath, vaultPath, contentLength: content.length }
    )
  }

  /**
   * Get stored file intelligence
   */
  async getStoredFileIntelligence(filePath: string, vaultPath: string): Promise<FileIntelligence | null> {
    console.log('[LABELS] 📂 fileAnalysisService: getStoredFileIntelligence called')
    console.log('[LABELS] 📂 filePath:', filePath)
    console.log('[LABELS] 📂 vaultPath:', vaultPath)

    return await this.executeOperationOrThrow(
      'getStoredFileIntelligence',
      async () => {
        // V02 COMPLIANT: Use unified client directly
        const apiResult: any = await intelligenceClient.read(filePath, vaultPath)
        const result = apiResult && apiResult.success !== false ? (apiResult.data || null) : null

        console.log('[LABELS] 📂 getStoredFileIntelligence result:', result ? 'found' : 'null')
        if (result) {
          console.log('[LABELS] 📂 Found intelligence with', result.labels?.length || 0, 'labels')
          console.log('[LABELS] 📂 Intelligence file_path in JSON:', result.file_path)

          // CORRUPTION DETECTION: Check for corrupted intelligence data
          if (!result.file_path || result.file_path === 'undefined' || result.file_path === 'null') {
            console.log('[LABELS] 🚨 CORRUPTION DETECTED: file_path is invalid:', result.file_path)
            console.log('[LABELS] 🚨 Returning null to trigger fresh analysis')
            console.log('[LABELS] 🧹 NOTE: Corrupted file will be overwritten on next analysis')
            return null
          }

          return result as FileIntelligence
        }

        return null
      },
      { filePath, vaultPath }
    )
  }
}

export const fileAnalysisService = new FileAnalysisService()
