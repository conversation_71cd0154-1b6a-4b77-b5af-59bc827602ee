# ChatLo Unified IPC System - System Design & Architecture (V02)

## What’s New in V02
- Incorporated the following reviews into architecture guidance and plans:
  - `plan/system architecture interim review.md`
  - `plan/Files page interim review.md`
  - `plan/History page interim review.md`
  - `plan/HomePage page interim review.md`
  - `plan/Chat page interim review.md`
- Added the unified core service plan (`plan/core_service_plan_001.md`) and aligned it with live APIs in `docs/API_REFERENCE.md`.
- Introduced new API categories: `intelligence:*` and `events:*` with sample payloads and usage patterns.

## Summary of Current Maturity
- Overall architecture maturity ~3.4/5 with strong IPC, DB schema, preview UX, and admin foundations.
- Gaps: path resolution consistency, complete vault IPC, plugin API extension, eventing, and unified intelligence storage/loader.

## Unified Core Architecture (Main Process)
- Core Services under `electron/core/`:
  - PathResolver: canonical path joins/normalization; platform-aware; strictly used by `vault:*` handlers
  - VaultCoreService: directory/file CRUD, scanning, registry; requires PathResolver
  - FileCoreService: file indexing, MIME/hash, plugin processing
  - IntelligenceCoreService: markdown→JSON parser, read/write `.intelligence`, sessions
  - PluginCoreService: plugin discovery; future namespace registration
  - EventBus: `file`, `intelligence`, `task` event categories with subscribe/unsubscribe
- IPC Namespaces:
  - `vault:*`, `files:*`, `intelligence:*`, `events:*`, `plugins:*`, `path:*`
- Renderer Clients (thin): `src/api/UnifiedAPIClient.ts` + typed wrappers; no FS logic in renderer
- Shared Types: `src/types/index.ts` as single source per Rule 2.3

## API Additions (V02)
- `intelligence:*` (read/write/listSessions/parseMarkdown) for stable intelligence IO and parsing
- `events:*` (subscribe/unsubscribe) with event payloads:
  - `file:added|changed|removed` { path, vaultId, size, modified }
  - `intelligence:updated` { filePath, vaultId, ideaCount, updatedAt }
  - `task:progress` { taskId, percent, message }
- `docs/API_REFERENCE.md` updated accordingly

## Page-Level Guidance

### Files Page — Explorer, Previews, Intelligence Overlay
- Strengths: robust PDF/markdown/text/image/code viewers; overlay wired to `SmartLabelingInterface`
- Pain points: path writes failing (mixed separators), missing persisted read-on-open; eventing absent
- Actions:
  - Use `vault:*` for all read/write (no string joins); PathResolver enforced in main
  - Load persisted intelligence first on overlay open; then listen to `intelligence:updated`
  - Add FS event bus to refresh listing/overlay on changes

### Home Page — Vault-Centric Entry & Ingest
- Strengths: solid vault-first UX; small-file streaming; batch organize
- Pain points: large-file uploads redirect to manual copy; path joins in renderer
- Actions:
  - Enable large-file system-IPC writes (no manual copy); progress UI; cancellation
  - Switch to canonical path helpers; tests for `generateUniqueFilename`

### History Page — Conversations & Artifacts
- Strengths: DB indices; artifact schema; nav back to chat/files
- Actions:
  - Add vault/model/pinned filters; FTS search; entity/topic chips
  - Subscribe to `events:*` for live updates

### Chat Page — Messaging & Context
- Strengths: resilient streaming pipeline; attachments and @refs
- Actions:
  - Header with active vault/model/privacy; event bus for tool actions
  - Tests for chunk assembly and error recovery

## Intelligence Storage Strategy
- Pick one canonical store and enforce across services:
  - Sessions: `<vault>/.intelligence/documents/<hash>/sessions/session_<ts>.json`
  - Single-file JSON: `<context>/.context/files/<hash>.json`
- IntelligenceCoreService centralizes both read/write and parser logic; renderer uses only `intelligence:*`

## Eventing Strategy
- EventBus in main
- Subscriptions via `events:subscribe` and `events:unsubscribe`
- Consumers: Files (listing refresh), Overlay (intelligence updates), History (conversation/artifact updates), Home (organize progress)

## Navigation & Deep Links (Optional, No Breakage)
- Files: `/files/:vaultId?path=<relative>&mode=explorer|master`
- Chat: `/chat/:conversationId?vaultId=<id>`
- History: `/history?vaultId=<id>&pinned=true&model=local`
- Keep adapters for legacy parameters in `useNavigation`

## Risks & Mitigations
- Path drift: disallow raw FS operations in renderer; enforce PathResolver at `vault:*`
- Parser brittleness: single parser service + tests; reject invalid payloads
- Event storms: debounce/coalesce; opt-in filters on subscribe
- Bundle bloat: aggressively exclude dev-only modules and unused assets from production packages; lazy-load heavy viewers (PDF.js, Mermaid)

### Deprecations (V02)
- `FileSystemManager.initializeChatloFolder()` → Use `initializeVaultRoot()`
- `FileSystemManager.getChatloFolderPath()` → Use `getVaultRootPath()`
- `FileSystemManager.setChatloFolderPath(path)` → Use `setVaultRootPath(path)`
- `FileSystemManager.saveContentAsFile(content, name, subfolder)` → Use `saveContentToVault(content, name, subfolder)`
- IPC:
  - `files:getChatloFolderPath` → `files:getVaultRootPath`
  - `files:setChatloFolderPath` → `files:setVaultRootPath`
  - `files:saveContentAsFile` → `files:saveContentToVault`

All deprecated endpoints remain available for backward compatibility in this version and log deprecation warnings.

## Acceptance Criteria
- Files Overlay loads persisted labels first; new labels update live; `.intelligence` writes exist on disk (Windows-verified)
- Files/History/Overlay refresh via events without reloads
- API docs include `intelligence:*` and `events:*` with examples

## Effort & Milestones
- Core additions: 4–6 days; IPC docs: 1–2; Renderer migrations: 2–4; Plugin namespaces: 3–4; Security toggle: 1–2; Tests: 2–3 (total ~8–12 person-days)

## Appendix: References
- System review: `plan/system architecture interim review.md`
- Files review: `plan/Files page interim review.md`
- History review: `plan/History page interim review.md`
- Home review: `plan/HomePage page interim review.md`
- Chat review: `plan/Chat page interim review.md`
- Core plan: `plan/core_service_plan_001.md`
- API reference: `docs/API_REFERENCE.md`
