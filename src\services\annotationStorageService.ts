/**
 * Annotation Storage Service
 * Handles smart annotation persistence using unified /files/ storage strategy
 */

import { SmartAnnotationNote, FileIntelligence } from '../types/fileIntelligenceTypes'
import { intelligence as intelligenceClient } from '../api/UnifiedAPIClient'
import { extractContextPath } from '../utils/vaultPath'

export class AnnotationStorageService {
  
  /**
   * Load all annotations for a file
   * STEP 1 FIX: Convert existing labels into first annotation if no annotations exist
   */
  async loadAnnotations(filePath: string): Promise<SmartAnnotationNote[]> {
    try {
      const contextPath = extractContextPath(filePath)
      console.log('[ANNOTATIONS] 📖 Loading annotations for:', { filePath, contextPath })

      const result = await intelligenceClient.read(filePath, contextPath)

      if (result && result.success !== false && result.data) {
        const intelligence = result.data as FileIntelligence

        // DEBUG: Log the full intelligence data structure
        console.log('[ANNOTATIONS] 🔍 DEBUG: Intelligence data structure:', {
          hasKeyIdeas: !!intelligence.key_ideas,
          keyIdeasCount: intelligence.key_ideas?.length || 0,
          hasSmartAnnotations: !!intelligence.smart_annotations,
          smartAnnotationsCount: intelligence.smart_annotations?.length || 0,
          keys: Object.keys(intelligence)
        })

        let annotations = intelligence.smart_annotations || []

        // STEP 1 FIX: If no annotations exist but labels do, create default annotation from labels
        if (annotations.length === 0 && intelligence.key_ideas && intelligence.key_ideas.length > 0) {
          console.log('[ANNOTATIONS] 📖 🔄 Converting', intelligence.key_ideas.length, 'labels to first annotation')

          // Create summary from labels
          const labelSummary = intelligence.key_ideas
            .map(idea => `• ${idea.text}`)
            .join('\n')

          const defaultAnnotation: SmartAnnotationNote = {
            id: 'default_labels_summary',
            type: 'summary',
            title: 'Document Summary',
            content: `Key insights from this document:\n\n${labelSummary}\n\nProcessing confidence: ${intelligence.processing_confidence || 0}%\nAnalyzed with: ${intelligence.analysis_metadata?.model_used || 'smart_labeling_interface'}`,
            created_at: intelligence.created_at || new Date().toISOString(),
            updated_at: intelligence.updated_at || new Date().toISOString(),
            note_number: 1,
            labels_snapshot: intelligence.key_ideas
          }

          annotations = [defaultAnnotation]
          console.log('[ANNOTATIONS] 📖 ✅ Created default annotation from labels')
        }

        console.log('[ANNOTATIONS] 📖 ✅ Loaded', annotations.length, 'annotations')
        return annotations
      }

      console.log('[ANNOTATIONS] 📖 No existing intelligence data found')
      return []
    } catch (error) {
      console.error('[ANNOTATIONS] 📖 ❌ Error loading annotations:', error)
      return []
    }
  }

  /**
   * Save a new annotation (preserves existing data)
   */
  async saveAnnotation(filePath: string, annotation: SmartAnnotationNote): Promise<boolean> {
    try {
      const contextPath = extractContextPath(filePath)
      console.log('[ANNOTATIONS] 💾 Saving annotation:', { filePath, contextPath, annotationId: annotation.id })
      
      // Load existing intelligence data
      const result = await intelligenceClient.read(filePath, contextPath)
      let intelligence: FileIntelligence
      
      if (result && result.success !== false && result.data) {
        intelligence = result.data as FileIntelligence
      } else {
        // Create new intelligence structure if none exists
        intelligence = {
          file_path: filePath,
          key_ideas: [],
          weighted_entities: [],
          human_connections: [],
          processing_confidence: 0,
          analysis_metadata: {
            processing_time_ms: 0,
            model_used: 'annotation_service',
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          smart_annotations: []
        }
      }
      
      // Add new annotation
      if (!intelligence.smart_annotations) {
        intelligence.smart_annotations = []
      }
      
      // Assign note number for pagination
      annotation.note_number = intelligence.smart_annotations.length + 1
      
      intelligence.smart_annotations.push(annotation)
      intelligence.updated_at = new Date().toISOString()
      
      // Save back to unified storage
      const saveResult = await intelligenceClient.write(filePath, contextPath, intelligence)
      
      if (saveResult && saveResult.success !== false) {
        console.log('[ANNOTATIONS] 💾 ✅ Annotation saved successfully')
        return true
      } else {
        console.error('[ANNOTATIONS] 💾 ❌ Failed to save annotation:', saveResult?.error)
        return false
      }
    } catch (error) {
      console.error('[ANNOTATIONS] 💾 ❌ Error saving annotation:', error)
      return false
    }
  }

  /**
   * Update an existing annotation
   */
  async updateAnnotation(filePath: string, annotationId: string, updates: Partial<SmartAnnotationNote>): Promise<boolean> {
    try {
      const contextPath = extractContextPath(filePath)
      console.log('[ANNOTATIONS] ✏️ Updating annotation:', { filePath, contextPath, annotationId })
      
      // Load existing intelligence data
      const result = await intelligenceClient.read(filePath, contextPath)
      
      if (!result || result.success === false || !result.data) {
        console.error('[ANNOTATIONS] ✏️ ❌ No intelligence data found for update')
        return false
      }
      
      const intelligence = result.data as FileIntelligence
      
      if (!intelligence.smart_annotations) {
        console.error('[ANNOTATIONS] ✏️ ❌ No annotations found for update')
        return false
      }
      
      // Find and update annotation
      const annotationIndex = intelligence.smart_annotations.findIndex(a => a.id === annotationId)
      
      if (annotationIndex === -1) {
        console.error('[ANNOTATIONS] ✏️ ❌ Annotation not found:', annotationId)
        return false
      }
      
      // Apply updates
      intelligence.smart_annotations[annotationIndex] = {
        ...intelligence.smart_annotations[annotationIndex],
        ...updates,
        updated_at: new Date().toISOString()
      }
      
      intelligence.updated_at = new Date().toISOString()
      
      // Save back to unified storage
      const saveResult = await intelligenceClient.write(filePath, contextPath, intelligence)
      
      if (saveResult && saveResult.success !== false) {
        console.log('[ANNOTATIONS] ✏️ ✅ Annotation updated successfully')
        return true
      } else {
        console.error('[ANNOTATIONS] ✏️ ❌ Failed to update annotation:', saveResult?.error)
        return false
      }
    } catch (error) {
      console.error('[ANNOTATIONS] ✏️ ❌ Error updating annotation:', error)
      return false
    }
  }

  /**
   * Delete an annotation
   */
  async deleteAnnotation(filePath: string, annotationId: string): Promise<boolean> {
    try {
      const contextPath = extractContextPath(filePath)
      console.log('[ANNOTATIONS] 🗑️ Deleting annotation:', { filePath, contextPath, annotationId })
      
      // Load existing intelligence data
      const result = await intelligenceClient.read(filePath, contextPath)
      
      if (!result || result.success === false || !result.data) {
        console.error('[ANNOTATIONS] 🗑️ ❌ No intelligence data found for deletion')
        return false
      }
      
      const intelligence = result.data as FileIntelligence
      
      if (!intelligence.smart_annotations) {
        console.error('[ANNOTATIONS] 🗑️ ❌ No annotations found for deletion')
        return false
      }
      
      // Remove annotation
      const originalLength = intelligence.smart_annotations.length
      intelligence.smart_annotations = intelligence.smart_annotations.filter(a => a.id !== annotationId)
      
      if (intelligence.smart_annotations.length === originalLength) {
        console.error('[ANNOTATIONS] 🗑️ ❌ Annotation not found for deletion:', annotationId)
        return false
      }
      
      // Renumber remaining annotations
      intelligence.smart_annotations.forEach((annotation, index) => {
        annotation.note_number = index + 1
      })
      
      intelligence.updated_at = new Date().toISOString()
      
      // Save back to unified storage
      const saveResult = await intelligenceClient.write(filePath, contextPath, intelligence)
      
      if (saveResult && saveResult.success !== false) {
        console.log('[ANNOTATIONS] 🗑️ ✅ Annotation deleted successfully')
        return true
      } else {
        console.error('[ANNOTATIONS] 🗑️ ❌ Failed to delete annotation:', saveResult?.error)
        return false
      }
    } catch (error) {
      console.error('[ANNOTATIONS] 🗑️ ❌ Error deleting annotation:', error)
      return false
    }
  }

  /**
   * Generate annotation using AI
   */
  async generateAnnotation(filePath: string, userPrompt: string, fileContent: string): Promise<SmartAnnotationNote | null> {
    try {
      console.log('[ANNOTATIONS] 🤖 Generating AI annotation for prompt:', userPrompt.substring(0, 100) + '...')

      // Create analysis prompt for the AI
      const analysisPrompt = `${userPrompt}

DOCUMENT CONTENT:
${fileContent.substring(0, 3000)}${fileContent.length > 3000 ? '\n\n[Content truncated for analysis...]' : ''}

Please provide a detailed analysis based on the user's request above. Focus on being specific and actionable.`

      // Try to use local model service for AI generation
      let aiResponse = ''
      try {
        // Import local model service dynamically to avoid circular dependencies
        const { localModelService } = await import('../services/localModelService')

        console.log('[ANNOTATIONS] 🤖 Using local model for annotation generation')
        aiResponse = await localModelService.generateResponse(analysisPrompt)

        if (!aiResponse || aiResponse.trim().length === 0) {
          throw new Error('Empty response from local model')
        }

        console.log('[ANNOTATIONS] 🤖 ✅ Local model generated response:', aiResponse.substring(0, 200) + '...')
      } catch (modelError) {
        console.warn('[ANNOTATIONS] 🤖 ⚠️ Local model unavailable, using fallback:', modelError)

        // Fallback response when local model is not available
        aiResponse = `Analysis for: "${userPrompt}"

Based on the document content provided, here are the key insights:

• The document appears to contain information relevant to your query
• Further analysis would require a more detailed examination of the content
• Consider reviewing the specific sections that relate to your question

Note: This is a fallback response. For more detailed AI analysis, please ensure a local language model is available and running.`
      }

      const annotation: SmartAnnotationNote = {
        id: `annotation_${Date.now()}`,
        type: 'ai',
        title: userPrompt.length > 50 ? userPrompt.substring(0, 50) + '...' : userPrompt,
        content: aiResponse,
        user_prompt: userPrompt,
        ai_response: aiResponse,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        labels_snapshot: [] // TODO: Get current labels from SmartLabelingInterface
      }

      console.log('[ANNOTATIONS] 🤖 ✅ AI annotation generated successfully')
      return annotation
    } catch (error) {
      console.error('[ANNOTATIONS] 🤖 ❌ Error generating AI annotation:', error)
      return null
    }
  }
}

// Export singleton instance
export const annotationStorageService = new AnnotationStorageService()
