### Chat Page — Interim Review (Messaging, Context, and API Alignment)

| Category | Current State | Evidence / Story (Experience, Services/Components, APIs) | Maturity (0–5) | Improvements |
|---|---|---|---:|---|
| Messaging Flow & Streaming | Streaming send implemented with user message persistence and chunk rendering | Story: User types and sends; store writes user message to <PERSON>; model is selected via settings; local vs external service provides streaming chunks which are appended to `streamingMessage` in store; upon completion assistant message is saved. Services: `localModelService` (LM Studio/Ollama streaming), `openRouter` streaming; Store: `useAppStore.sendStreamingMessage`. IPC: `electronAPI.db.addMessage`. | 4.0 | Consolidate chunk handling UI into Chat component with token/latency stats. Add backpressure and pause/resume. Surface model source (local/external) and privacy status per message. |
| Context Attachment & File References | Files can be attached; inline `@file` references merged into context for model input | Story: On send, store inspects attachments and `@refs`, builds `fileContext` via `buildFileContext`, warns on unvectorized files. | 4.0 | Show attached files with status (indexed/vectorized). Offer quick "Process now" when unvectorized. Cache per-conversation context summaries. |
| Vault Context Targeting | Target conversation is aligned; optional selected context id | Story: `sendStreamingMessage` accepts `selectedContextId`; navigation hooks pre-select context from Home. | 3.5 | Persist and display active vault context in the Chat header; offer switcher to change target vault for pinning/intelligence. |
| Persistence & History | DB writes for messages; indices optimized | Story: Messages stored via IPC to SQLite; indices on conversation/time; supports retrieval in HistoryPage. | 4.5 | Add conversation-level metadata (pinned topics, last model used) for filtering in History. |
| Local-First Model Selection | Local models preferred; external fallback supported | Story: `localModelService` streams from LM Studio/Ollama; `openRouter` provides external fallback with strict error handling and validation. | 4.0 | Expose per-conversation model pinning and quick switch. Indicate costs when using external models. |
| Error Handling & UX | Structured ServiceError paths; user message saved before model call | Story: Store wraps calls; on errors, user sees stable history and can retry; services map HTTP → error codes. | 4.0 | Add retry on network hiccups, show partial responses; add "copy last error details" action. |
| Eventing & Realtime Feedback | Streaming text updates appear; no centralized event bus | Story: UI subscribes to store; services push chunks to callbacks; no global event channel. | 3.0 | Introduce event bus for typing indicators, tool events, and plugin actions; unify with Files/History notifications. |
| Accessibility & Keyboard | Standard input supports typing; basic shortcuts likely | Story: Typical input with Enter-to-send; not fully audited. | 3.0 | Add ARIA labels, keyboard shortcuts for send/attach/switch model; focus management on streaming completion. |
| Testing Readiness | Store methods are testable; service adapters abstract I/O | Story: `sendStreamingMessage`, context building, and DB IPC seams are unit-testable. | 3.5 | Add tests for streaming chunk assembly, error branches, attachment parsing, and draft → real conversation transition. |

### Overall Alignment
- Chat page is well aligned with the local-first, API-driven design: robust streaming pipeline, persistence, and file-aware context. Main gaps are visibility of context/model status, event unification, and accessibility/testing polish.

### High-Impact Improvements
- Header context bar showing active vault, model, privacy mode, and quick switches.
- Unified event bus for streaming states and tool/plugin actions.
- Attachment status indicators (indexed/vectorized) with one-click processing.
- Tests for chunk assembly, error recovery, and context reference parsing.
