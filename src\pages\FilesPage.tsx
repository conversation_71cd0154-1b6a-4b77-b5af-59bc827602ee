import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useSearchParams, useNavigate } from 'react-router-dom'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS, getFileTypeIcon } from '../components/Icons/index'

// import { ArtifactsSidebar } from '../components/artifacts/ArtifactsSidebar'

import { FileTreeNode } from '../types'
import { vaultUIManager } from '../services/vaultUIManager'
import { contextVaultService } from '../services/contextVaultService'
import { smartInstructionService, SmartInstructionRequest } from '../services/smartInstructionService'
import { useAppStore } from '../store'
import { ContextVaultSelector } from '../components/ContextVaultSelector'
import { EnhancedVaultSelector } from '../components/EnhancedVaultSelector'
import { QuickActionsToolbar } from '../components/QuickActionsToolbar'
import ModelSelectionOverlay from '../components/ModelSelectionOverlay'
import { FileSearchFilter, FileFilters } from '../components/FileSearchFilter'
import { FileOperationsHub } from '../components/FileOperationsHub'
import { FileContextMenu } from '../components/FileContextMenu'
import { FilePageOverlay } from '../components/FilePageOverlay'
import { pdfViewerService } from '../services/PDFViewerService'
import { fileViewerService } from '../services/FileViewerService'



// FileTreeNode interface is now imported from types

interface ViewModeState {
  currentMode: 'explorer' | 'master'
  showArtifacts: boolean
  artifactsExpanded: boolean
}



const FilesPage: React.FC = () => {
  const { contextId } = useParams()
  const [searchParams, setSearchParams] = useSearchParams()
  const navigate = useNavigate()
  // const { artifactsVisible } = useAppStore()

  // Get context and navigation hints from URL params
  const contextFromUrl = searchParams.get('context') || contextId || undefined
  const pathFromUrl = searchParams.get('path') || ''
  const modeFromUrl = (searchParams.get('mode') as 'explorer' | 'master') || undefined

  // State for vault data
  const [fileTree, setFileTree] = useState<FileTreeNode[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dragOver, setDragOver] = useState<string | null>(null)

  // State for UI
  const [viewMode, setViewMode] = useState<ViewModeState>({
    currentMode: 'explorer', // Start with explorer, will switch to master when master.md is found
    showArtifacts: false,
    artifactsExpanded: false
  })

  const [selectedFile, setSelectedFile] = useState<string | null>(null)
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null)
  const [selectedContextId, setSelectedContextId] = useState<string | null>(null)
  const [folderFiles, setFolderFiles] = useState<FileTreeNode[]>([])
  const [folderLoading, setFolderLoading] = useState<boolean>(false)
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())
  const [masterContent, setMasterContent] = useState<string>('')
  const [masterLoading, setMasterLoading] = useState(false)

  // State for remembering last context vault (for Master mode UX)
  const [lastContextVaultId, setLastContextVaultId] = useState<string | null>(null)
  const [pendingMasterMode, setPendingMasterMode] = useState<boolean>(false)

  // State for enhanced file operations
  const [searchQuery, setSearchQuery] = useState('')
  const [fileFilters, setFileFilters] = useState<FileFilters>({
    fileType: 'all',
    sortBy: 'name',
    sortOrder: 'asc'
  })
  const [filteredFileTree, setFilteredFileTree] = useState<FileTreeNode[]>([])
  const [useEnhancedSelector] = useState(true)

  // Context menu state
  const [contextMenu, setContextMenu] = useState<{
    isOpen: boolean
    position: { x: number; y: number }
    targetFile?: string
    targetPath?: string
  }>({
    isOpen: false,
    position: { x: 0, y: 0 }
  })

  // File operations hub state
  const [isHubOpen, setIsHubOpen] = useState(false)

  // Smart Instruction state
  const [smartInstruction, setSmartInstruction] = useState('')
  const [isProcessingInstruction, setIsProcessingInstruction] = useState(false)
  const [instructionResult, setInstructionResult] = useState<{
    success: boolean
    message: string
    changes?: string[]
    metadata?: {
      processingTime: number
      confidence: number
      modelUsed?: string
      isLocalModel: boolean
    }
  } | null>(null)
  const [smartInstructionModel, setSmartInstructionModel] = useState<string>('')
  const [isModelSelectionOpen, setIsModelSelectionOpen] = useState(false)



  // File page overlay state (supports PDF, Markdown, Images, Text, Code)
  const [isPDFViewerOpen, setIsPDFViewerOpen] = useState(false)

  // Check if file type is supported by the hub
  const isSupportedFileType = (fileName: string): boolean => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    const supportedExtensions = [
      // Text files
      'txt', 'md', 'json', 'xml', 'csv',
      // Code files
      'js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'h', 'css', 'html', 'php', 'rb', 'go', 'rs', 'swift',
      // Documents
      'pdf', 'docx', 'doc', 'rtf',
      // Images
      'png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg', 'webp',
      // Archives (for preview info)
      'zip', 'rar', '7z', 'tar', 'gz'
    ]
    return ext ? supportedExtensions.includes(ext) : false
  }

  // Check if file type is supported by the new FilePageOverlay
  const isFilePageOverlaySupported = (fileName: string): boolean => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    const overlayExtensions = [
      // PDF files
      'pdf',
      // Markdown files
      'md', 'markdown',
      // Text files
      'txt', 'log', 'csv', 'xml', 'json', 'yaml', 'yml',
      // Code files
      'js', 'ts', 'tsx', 'jsx', 'html', 'css', 'scss', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt',
      // Image files
      'jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'bmp', 'ico'
    ]
    return ext ? overlayExtensions.includes(ext) : false
  }

  // Load vault file tree on mount
  useEffect(() => {
    console.log('FilesPage mounted, loading file tree...')
    loadFileTree()
  }, [])

  // Filter and sort file tree based on search and filters
  useEffect(() => {
    let filtered = [...fileTree]

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(node =>
        node.name.toLowerCase().includes(query) ||
        node.path.toLowerCase().includes(query)
      )
    }

    // Apply file type filter
    if (fileFilters.fileType !== 'all') {
      filtered = filtered.filter(node => {
        if (node.type === 'folder') return true

        const ext = node.name.split('.').pop()?.toLowerCase() || ''
        switch (fileFilters.fileType) {
          case 'documents':
            return ['md', 'txt', 'pdf', 'doc', 'docx'].includes(ext)
          case 'images':
            return ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(ext)
          case 'code':
            return ['js', 'ts', 'tsx', 'jsx', 'html', 'css', 'py', 'java', 'cpp', 'c'].includes(ext)
          case 'archives':
            return ['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)
          default:
            return true
        }
      })
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0

      // Folders first
      if (a.type === 'folder' && b.type === 'file') return -1
      if (a.type === 'file' && b.type === 'folder') return 1

      switch (fileFilters.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'modified':
          comparison = new Date(a.modified || 0).getTime() - new Date(b.modified || 0).getTime()
          break
        case 'size':
          comparison = (a.size || 0) - (b.size || 0)
          break
        case 'type':
          const extA = a.name.split('.').pop() || ''
          const extB = b.name.split('.').pop() || ''
          comparison = extA.localeCompare(extB)
          break
      }

      return fileFilters.sortOrder === 'desc' ? -comparison : comparison
    })

    setFilteredFileTree(filtered)
  }, [fileTree, searchQuery, fileFilters]) // Run once on mount

  // Reload when context changes
  useEffect(() => {
    if (contextFromUrl) {
      console.log('Context changed to:', contextFromUrl, 'reloading file tree...')
      loadFileTree()
    }
  }, [contextFromUrl])

  // Reload when selected context changes (including when it becomes empty for shared dropbox)
  useEffect(() => {
    console.log('Selected context changed to:', selectedContextId || 'shared dropbox', 'reloading file tree...')

    // Track last context vault for Master mode UX
    if (selectedContextId) {
      setLastContextVaultId(selectedContextId)
      console.log('Remembering last context vault:', selectedContextId)

      // If we were pending master mode, switch to it now
      if (pendingMasterMode) {
        console.log('Context switched, now switching to Master mode')
        setPendingMasterMode(false)
        setViewMode(prev => ({ ...prev, currentMode: 'master', showArtifacts: true }))
        setSelectedFile('master.md')
      } else {
        // When selecting a context vault from menu, default to Master mode
        console.log('Context vault selected from menu, switching to Master mode')
        setViewMode(prev => ({ ...prev, currentMode: 'master', showArtifacts: true }))
        setSelectedFile('master.md')
      }
    }

    // Auto-switch to Explorer mode when shared dropbox is selected
    if (!selectedContextId) {
      console.log('Shared dropbox selected, switching to Explorer mode')
      setViewMode(prev => ({ ...prev, currentMode: 'explorer', showArtifacts: false }))
      setPendingMasterMode(false) // Clear any pending master mode
      setSelectedFile(null) // Clear selected file
    }

    loadFileTree()
  }, [selectedContextId, pendingMasterMode])

  // Debug: Log when fileTree changes
  useEffect(() => {
    console.log('File tree updated:', fileTree.length, 'items')
    console.log('Expanded folders:', Array.from(expandedFolders))
    console.log('Selected file:', selectedFile)
    console.log('Selected folder:', selectedFolder)
    console.log('Current view mode:', viewMode.currentMode)
  }, [fileTree, expandedFolders, selectedFile, selectedFolder, viewMode])

  // Auto-select first folder when file tree loads
  useEffect(() => {
    if (fileTree.length > 0 && !selectedFolder) {
      // Find the first context folder (skip vault folders)
      const findFirstContext = (nodes: FileTreeNode[]): string | null => {
        for (const node of nodes) {
          if (node.type === 'folder' && node.children) {
            // Look for context folders within vault folders
            for (const child of node.children) {
              if (child.type === 'folder') {
                return child.path
              }
            }
          }
        }
        return null
      }

      const firstContextPath = findFirstContext(fileTree)
      if (firstContextPath) {
        console.log('Auto-selecting first context folder:', firstContextPath)
        setSelectedFolder(firstContextPath)
        loadFolderFiles(firstContextPath)
      }
    }
  }, [fileTree, selectedFolder])

  // Persist expanded folders per context
  useEffect(() => {
    const key = selectedContextId ? `files_expanded_${selectedContextId}` : undefined
    if (!key) return
    try {
      const raw = localStorage.getItem(key)
      if (raw) {
        const arr = JSON.parse(raw) as string[]
        setExpandedFolders(new Set(arr))
      }
    } catch {}
  }, [selectedContextId])

  useEffect(() => {
    const key = selectedContextId ? `files_expanded_${selectedContextId}` : undefined
    if (!key) return
    try {
      localStorage.setItem(key, JSON.stringify(Array.from(expandedFolders)))
    } catch {}
  }, [expandedFolders, selectedContextId])

  // Route: reflect folder/file selection back to URL as relative path within context
  const updateUrlPath = async (absolutePath: string | null, mode?: 'explorer' | 'master') => {
    const next = new URLSearchParams(searchParams)
    if (mode) next.set('mode', mode)
    if (!absolutePath || !selectedContextId) {
      next.delete('path')
      setSearchParams(next, { replace: true })
      return
    }
    try {
      const context = contextVaultService.findContextById(selectedContextId)
      const vaultRoot = context?.vault?.path
      if (vaultRoot) {
        // Compute relative path by stripping vaultRoot prefix (best effort)
        const normalized = await window.electronAPI.invoke('path:normalize', absolutePath)
        const rel = String(normalized?.path || absolutePath).replace(vaultRoot.replace(/\\/g,'/'), '').replace(/^\/+/, '')
        if (rel) next.set('path', rel)
        else next.delete('path')
      }
    } catch {}
    setSearchParams(next, { replace: true })
  }

  // Apply URL hints (path, mode) when context changes or URL changes
  useEffect(() => {
    if (!contextFromUrl) return
    if (modeFromUrl) {
      setViewMode(prev => ({ ...prev, currentMode: modeFromUrl, showArtifacts: modeFromUrl === 'master' }))
    }

    ;(async () => {
      if (!pathFromUrl) return
      try {
        const ctx = contextVaultService.findContextById(contextFromUrl)
        const vaultRoot = ctx?.vault?.path
        if (!vaultRoot) return
        const joined = await window.electronAPI.invoke('path:join', vaultRoot, pathFromUrl)
        const absolutePath = joined?.path || `${vaultRoot}/${pathFromUrl}`
        // Try treat as folder first
        const dir = await window.electronAPI.vault.readDirectory(absolutePath).catch(() => null)
        if (dir?.success) {
          selectFolder(absolutePath)
        } else {
          selectFile(absolutePath)
        }
      } catch {}
    })()
  }, [contextFromUrl, pathFromUrl, modeFromUrl])

  // Subscribe to file change events filtered by selected context vault path
  useEffect(() => {
    let unsubscribeRenderer: (() => void) | null = null
    let subscriptionId: string | null = null

    const setup = async () => {
      try {
        if (!selectedContextId) return
        const ctx = contextVaultService.findContextById(selectedContextId)
        const vaultRoot = ctx?.vault?.path
        if (!vaultRoot) return
        const sub = await window.electronAPI.invoke('events:subscribe', 'file', { pathPrefix: vaultRoot, eventNames: ['file:changed','file:removed','file:indexed','file:processed'] })
        subscriptionId = sub?.subscriptionId
        if (!subscriptionId) return
        const channel = `events:subscription:${subscriptionId}`
        const handler = (_payload: any) => {
          const { eventName, payload: data } = _payload || {}
          if (selectedFolder && typeof data?.path === 'string' && data.path.startsWith(selectedFolder)) {
            loadFolderFiles(selectedFolder)
          } else {
            loadFileTree()
          }
        }
        // Attach listener via invoke/IPC exposed in preload
        const remove = (window as any).electronAPI?.events?.on
          ? (window as any).electronAPI.events.on(channel, handler)
          : (() => {
              // Fallback: attach via ipcRenderer if available (dev only)
              try {
                const { ipcRenderer } = require('electron')
                ipcRenderer.on(channel, (_: any, data: any) => handler(data))
                return () => ipcRenderer.removeAllListeners(channel)
              } catch {
                return () => {}
              }
            })()
        unsubscribeRenderer = remove
      } catch (e) {
        console.warn('Failed to subscribe to file events', e)
      }
    }

    setup()

    return () => {
      try { unsubscribeRenderer && unsubscribeRenderer() } catch {}
      if (subscriptionId) {
        window.electronAPI.invoke('events:unsubscribe', subscriptionId).catch(() => {})
      }
    }
  }, [selectedContextId, selectedFolder])

  const loadFileTree = async () => {
    try {
      setLoading(true)
      setError(null)

      // Use selectedContextId if available, otherwise fall back to contextFromUrl
      const contextId = selectedContextId || contextFromUrl
      console.log('Loading file tree for context:', contextId)

      const tree = await vaultUIManager.getFileTree(contextId || undefined)
      console.log('Raw file tree from vaultUIManager:', tree)
      setFileTree(tree)

      // Auto-expand and select first master.md (like GitHub README)
      if (tree && tree.length > 0) {
        console.log('Processing file tree with', tree.length, 'root nodes')
        const expandedPaths = new Set<string>()
        let firstMasterPath: string | null = null

        // Function to recursively find master.md and expand folders
        const findMasterAndExpand = (nodes: FileTreeNode[], parentPath: string = '') => {
          console.log('Processing nodes at level:', parentPath, 'nodes:', nodes.length)
          for (const node of nodes) {
            console.log('Processing node:', node.name, 'type:', node.type, 'path:', node.path)
            if (node.type === 'folder') {
              // Auto-expand all vault and context folders
              expandedPaths.add(node.path)
              console.log('Added to expanded paths:', node.path)

              if (node.children) {
                findMasterAndExpand(node.children, node.path)
              }
            } else if (node.type === 'file' && node.name === 'master.md' && !firstMasterPath) {
              // Found the first master.md
              firstMasterPath = node.path
              console.log('Found first master.md at:', firstMasterPath)
            } else if (node.type === 'file') {
              console.log('Found file:', node.name, 'at:', node.path)
            }
          }
        }

        findMasterAndExpand(tree)

        // Update state with expanded folders and selected master.md
        console.log('Setting expanded folders:', Array.from(expandedPaths))
        setExpandedFolders(expandedPaths)

        if (firstMasterPath) {
          console.log('Setting selected file and switching to master mode:', firstMasterPath)
          setSelectedFile(firstMasterPath)

          // Use setTimeout to ensure state updates are processed
          setTimeout(() => {
            setViewMode(prev => {
              console.log('Switching view mode from', prev.currentMode, 'to master')
              return { ...prev, currentMode: 'master' }
            })
            // Load the master.md content
            loadMasterContent(firstMasterPath!)
          }, 100)
        } else {
          console.log('No master.md found in file tree')
        }
      }
    } catch (err: any) {
      console.error('Error loading file tree:', err)
      setError(err.message || 'Failed to load file tree')
    } finally {
      setLoading(false)
    }
  }

  // Add CSS styles for file tree interactions
  React.useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      .file-tree-item {
        transition: all 0.2s ease;
      }
      .file-tree-item:hover {
        background-color: rgba(138, 176, 187, 0.1);
      }
      .file-tree-item.selected {
        background-color: rgba(138, 176, 187, 0.2);
        border-left: 2px solid #8AB0BB;
      }
      .file-tree-item.drop-target {
        background-color: rgba(138, 176, 187, 0.2);
        border: 2px dashed #8AB0BB;
      }
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])



  const selectFile = (path: string) => {
    setSelectedFile(path)
    updateUrlPath(path, 'explorer')
    // If selecting master.md, load its content and switch to master mode
    if (path.endsWith('master.md')) {
      loadMasterContent(path)
      setViewMode(prev => ({ ...prev, currentMode: 'master', showArtifacts: true }))
      setIsHubOpen(false)
      setIsPDFViewerOpen(false)
    } else {
      // For any other file, switch to explorer mode
      setViewMode(prev => ({ ...prev, currentMode: 'explorer', showArtifacts: false }))

      const fileName = path.split('/').pop() || path
      const fileExtension = fileName.split('.').pop()?.toLowerCase()

      // Handle files supported by FilePageOverlay (PDF, Markdown, Images, Text, Code)
      if (isFilePageOverlaySupported(fileName)) {
        fileViewerService.openFile(path, fileName) // Use generic file viewer service
        setIsPDFViewerOpen(true)
        setIsHubOpen(false)
      } else if (isSupportedFileType(fileName)) {
        // Open file operations hub for other supported file types (Office docs, archives, etc.)
        setIsHubOpen(true)
        setIsPDFViewerOpen(false)
      } else {
        setIsHubOpen(false)
        setIsPDFViewerOpen(false)
      }
    }
  }

  const selectFolder = (path: string) => {
    setSelectedFolder(path)
    updateUrlPath(path, 'explorer')
    loadFolderFiles(path)
    // Switch to explorer mode when selecting a folder
    setViewMode(prev => ({ ...prev, currentMode: 'explorer', showArtifacts: false }))
    setIsHubOpen(false)
    setIsPDFViewerOpen(false)
  }

  // Close the file operations hub
  const closeHub = () => {
    setIsHubOpen(false)
    // Keep the file selected but show explorer mode
  }



  // Close the PDF viewer overlay
  const closePDFViewer = () => {
    setIsPDFViewerOpen(false)
    pdfViewerService.closePDF()
    // Keep the file selected but show explorer mode
  }

  // Handle double-click on files in explorer mode
  const handleFileDoubleClick = (filePath: string) => {
    // Check if it's a folder by looking at the folderFiles data
    const fileNode = folderFiles.find(f => f.path === filePath)

    if (fileNode?.type === 'folder') {
      // Navigate into the folder
      selectFolder(filePath)
    } else {
      // It's a file - use the same logic as selectFile
      selectFile(filePath)
    }
  }

  // Handle right-click on files in explorer mode
  const handleFileRightClick = (event: React.MouseEvent, filePath: string) => {
    const fileName = filePath.split('/').pop() || ''
    setContextMenu({
      isOpen: true,
      position: { x: event.clientX, y: event.clientY },
      targetFile: fileName,
      targetPath: filePath
    })
  }

  const loadMasterContent = async (filePath: string) => {
    try {
      setMasterLoading(true)

      // Check if electronAPI is available
      if (!window.electronAPI?.vault?.readFile) {
        setMasterContent('# Master Document\n\nContent not available in development mode.')
        return
      }

      const result = await window.electronAPI.vault.readFile(filePath)
      if (result.success && result.content) {
        setMasterContent(result.content)
      } else {
        setMasterContent('# Error\n\nFailed to load master document content.')
      }
    } catch (error) {
      console.error('Error loading master content:', error)
      setMasterContent('# Error\n\nFailed to load master document content.')
    } finally {
      setMasterLoading(false)
    }
  }

  const loadFolderFiles = async (folderPath: string) => {
    try {
      setFolderLoading(true)
      console.log('Loading files from folder:', folderPath)

      // Check if electronAPI is available
      if (!window.electronAPI?.vault?.readDirectory) {
        console.warn('ElectronAPI not available, using mock data')
        setFolderFiles([])
        return
      }

      const result = await window.electronAPI.vault.readDirectory(folderPath)
      if (result.success && result.items) {
        // Convert directory items to FileTreeNode format
        const files: FileTreeNode[] = result.items
          .filter(item => !item.name.startsWith('.')) // Filter out hidden files
          .map(item => ({
            type: item.isDirectory ? 'folder' : 'file',
            name: item.name,
            path: item.path,
            icon: item.isDirectory ? ICONS.folder : getFileIcon(item.name),
            color: item.isDirectory ? 'text-supplement2' : getFileColor(item.name),
            size: item.size,
            modified: item.modified
          }))

        setFolderFiles(files)
        console.log('Folder files loaded successfully:', files.length, 'items')
      } else {
        console.error('Failed to load folder files:', result.error)
        setFolderFiles([])
      }
    } catch (error) {
      console.error('Error loading folder files:', error)
      setFolderFiles([])
    } finally {
      setFolderLoading(false)
    }
  }

  // Use the centralized file type icon function
  const getFileIcon = (fileName: string) => {
    const result = getFileTypeIcon(fileName)
    return result?.icon || ICONS.file // Fallback to generic file icon
  }

  const getFileColor = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'md': return 'text-primary'
      case 'pdf': return 'text-red-400'
      case 'docx': case 'doc': return 'text-blue-400'
      case 'xlsx': case 'xls': return 'text-green-400'
      case 'pptx': case 'ppt': return 'text-orange-400'
      case 'png': case 'jpg': case 'jpeg': case 'gif': return 'text-purple-400'
      case 'js': case 'ts': case 'jsx': case 'tsx': return 'text-yellow-400'
      case 'py': return 'text-green-400'
      case 'json': return 'text-yellow-400'
      default: return 'text-gray-400'
    }
  }

  // File drop handlers
  const handleDragOver = (e: React.DragEvent, folderPath?: string) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(folderPath || 'general')
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(null)
  }

  const handleFileDrop = async (e: React.DragEvent, folderPath?: string) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(null)

    const files = Array.from(e.dataTransfer.files)
    if (files.length === 0) return

    try {
      console.log(`Dropping ${files.length} files to folder:`, folderPath)

      // Use the vault file handler to upload files
      const { vaultFileHandler } = await import('../services/vaultFileHandler')

      for (const file of files) {
        try {
          console.log(`📁 Uploading ${file.name} to ${folderPath}`)

          // Upload file to the specific folder path
          const uploadResults = await vaultFileHandler.replaceSharedDropboxService(
            [file],
            folderPath ? { type: 'folder', path: folderPath } : undefined,
            (fileName, progress) => {
              console.log(`📊 Upload progress for ${fileName}: ${progress.percentage.toFixed(1)}%`)
            }
          )

          if (uploadResults.length > 0 && uploadResults[0].success) {
            console.log(`✅ Successfully uploaded ${file.name}`)
          } else {
            console.error(`❌ Failed to upload ${file.name}`)
          }
        } catch (fileError) {
          console.error(`❌ Error uploading ${file.name}:`, fileError)
        }
      }

      // Show success message
      const successCount = files.length // Simplified for now
      if (successCount > 0) {
        // Use toast notification if available
        if (window.electronAPI?.toast) {
          window.electronAPI.toast.success(`✅ ${successCount} file(s) uploaded successfully!`)
        } else {
          alert(`✅ ${successCount} file(s) uploaded successfully!`)
        }
      }

      // Refresh file tree after drop
      await loadFileTree()

      // Navigate to the folder view to show the uploaded files (more human!)
      if (folderPath) {
        // Expand the target folder and select it
        setExpandedFolders(prev => {
          const newSet = new Set(prev)
          newSet.add(folderPath)
          return newSet
        })

        // Set the folder as selected to show its contents
        setSelectedFile(folderPath)
        console.log(`📁 Navigated to folder view: ${folderPath}`)
      }
    } catch (error) {
      console.error('Error dropping files:', error)
      if (window.electronAPI?.toast) {
        window.electronAPI.toast.error('❌ Error uploading files')
      } else {
        alert('❌ Error uploading files')
      }
    }
  }

  const handleModeChange = async (mode: 'explorer' | 'master') => {
    // Handle Master mode UX logic
    if (mode === 'master') {
      // If currently in shared dropbox, switch to a context vault
      if (!selectedContextId) {
        console.log('Switching to Master mode from shared dropbox')

        // Try to use last context vault
        let targetContextId = lastContextVaultId

        // If no last context, fallback to personal vault's first context
        if (!targetContextId) {
          const vaults = contextVaultService.getCurrentVaults()
          const personalVault = vaults.find(v => v.name.toLowerCase().includes('personal'))
          if (personalVault && personalVault.contexts.length > 0) {
            targetContextId = personalVault.contexts[0].id
            console.log('Fallback to personal vault first context:', targetContextId)
          } else {
            // If no personal vault, use any first available context
            const allContexts = contextVaultService.getAllContexts()
            if (allContexts.length > 0) {
              targetContextId = allContexts[0].id
              console.log('Fallback to first available context:', targetContextId)
            }
          }
        }

        // Switch to the target context
        if (targetContextId) {
          console.log('Switching to context for Master mode:', targetContextId)
          setPendingMasterMode(true) // Set flag to switch to master mode after context change
          contextVaultService.setSelectedContext(targetContextId)
          // The context change will be handled by the useEffect
          return // Don't set view mode yet, let the context change handle it
        } else {
          console.warn('No context available for Master mode')
          // Stay in explorer mode if no contexts available
          return
        }
      }

      // Auto-select master.md when switching to master mode
      setSelectedFile('master.md')
    }

    setViewMode(prev => ({
      ...prev,
      currentMode: mode,
      showArtifacts: mode === 'master'
    }))
  }

  // Handler for file upload
  const handleFileUpload = async (files: FileList) => {
    console.log('File upload requested:', files.length, 'files')
    // TODO: Implement file upload logic
    // This would integrate with the existing file drop functionality
  }

  // Handler for new file creation
  const handleNewFile = async () => {
    console.log('New file creation requested')
    // TODO: Implement new file creation logic
    // This could open a modal to get file name and type
  }

  // Handler for new folder creation
  const handleNewFolder = async () => {
    console.log('New folder creation requested')
    // TODO: Implement new folder creation logic
    // This could open a modal to get folder name
  }

  // Handler for file operations from the hub
  const handleFileAction = async (action: string, data?: any) => {
    console.log('File action requested:', action, data)

    switch (action) {
      case 'rename':
        // TODO: Implement file rename
        console.log('Rename file:', data)
        break

      case 'delete':
        // TODO: Implement file deletion with confirmation
        console.log('Delete file:', data)
        break

      case 'duplicate':
        // TODO: Implement file duplication
        console.log('Duplicate file:', data)
        break

      case 'add-to-context':
        // TODO: Implement context vault addition
        console.log('Add to context:', data)
        break

      case 'remove-from-context':
        // TODO: Implement context vault removal
        console.log('Remove from context:', data)
        break

      case 'summarize':
        // TODO: Implement AI summarization
        console.log('Summarize file:', data)
        break

      case 'chat-with-file':
        handleChatWithFile(data)
        break

      default:
        console.log('Unhandled file action:', action, data)
        break
    }
  }

  // Context menu handlers
  const handleContextMenu = (event: React.MouseEvent, file: string, path: string) => {
    event.preventDefault()
    setContextMenu({
      isOpen: true,
      position: { x: event.clientX, y: event.clientY },
      targetFile: file,
      targetPath: path
    })
  }

  const closeContextMenu = () => {
    setContextMenu(prev => ({ ...prev, isOpen: false }))
  }

  const handleChatWithFile = async (data?: any) => {
    try {
      const filePath = data?.path || contextMenu.targetPath
      const fileName = data?.file || contextMenu.targetFile

      if (!filePath || !fileName) {
        console.error('No file selected for chat')
        return
      }

      // Create a new conversation with the file attached
      const conversationId = await window.electronAPI.db.createConversation(`Chat with ${fileName}`)

      // Process the file for attachment
      if (window.electronAPI.files?.processFile) {
        const processedFile = await window.electronAPI.files.processFile(filePath)

        if (processedFile.success) {
          // Navigate to chat with the file attached
          const chatUrl = `/chat?conversation=${conversationId}&file=${encodeURIComponent(filePath)}&name=${encodeURIComponent(fileName)}`
          window.location.href = chatUrl
        } else {
          console.error('Failed to process file:', processedFile.error)
          // Still navigate to chat, user can manually attach
          window.location.href = `/chat?conversation=${conversationId}`
        }
      } else {
        // Fallback - just navigate to chat
        window.location.href = `/chat?conversation=${conversationId}`
      }
    } catch (error) {
      console.error('Error creating chat with file:', error)
    }
  }

  const handleContextMenuAction = async (action: string, data?: any) => {
    console.log('Context menu action:', action, data)

    // Handle context menu specific actions
    switch (action) {
      case 'preview':
        // Open file in preview mode using FilePageOverlay
        if (data?.path && data?.file) {
          const fileName = data.file
          if (isFilePageOverlaySupported(fileName)) {
            fileViewerService.openFile(data.path, fileName)
            setIsPDFViewerOpen(true)
            setIsHubOpen(false)
          }
        }
        break

      case 'edit':
        // Open file in edit mode using FilePageOverlay
        if (data?.path && data?.file) {
          const fileName = data.file
          if (isFilePageOverlaySupported(fileName)) {
            fileViewerService.openFile(data.path, fileName, undefined, true)
            setIsPDFViewerOpen(true)
            setIsHubOpen(false)
          }
        }
        break

      case 'copy-path':
        // Copy file path to clipboard
        if (data?.path) {
          navigator.clipboard.writeText(data.path)
          console.log('Copied path to clipboard:', data.path)
          // TODO: Show toast notification
        }
        break

      case 'show-in-explorer':
        // Show file in system explorer
        if (data?.path) {
          if (window.electronAPI?.shell?.showItemInFolder) {
            window.electronAPI.shell.showItemInFolder(data.path)
          }
        }
        break

      case 'open':
        if (data?.file) {
          setSelectedFile(data.file)
        }
        break

      case 'open-external':
        // Open with system default application
        if (data?.path || contextMenu.targetPath) {
          const path = data?.path || contextMenu.targetPath
          if (window.electronAPI?.shell?.openPath) {
            window.electronAPI.shell.openPath(path)
          }
        }
        break

      case 'copy':
        // TODO: Implement file copy to clipboard
        console.log('Copy file:', data)
        break

      case 'cut':
        // TODO: Implement file cut operation
        console.log('Cut file:', data)
        break

      case 'rename':
        // Handle file rename
        if (data?.path && data?.file) {
          const currentName = data.file
          const currentPath = data.path
          const newName = prompt('Enter new file name:', currentName)

          if (newName && newName !== currentName && newName.trim()) {
            // Simple path manipulation without imports
            // Use main-process path join to build new path
            const normalized = await window.electronAPI.invoke('path:normalize', currentPath)
            const baseDir = normalized?.path?.replace(/[\\/][^\\/]*$/, '') || currentPath.replace(/[\\/][^\\/]*$/, '')
            const joinRes = await window.electronAPI.invoke('path:join', baseDir, newName.trim())
            const newPath = joinRes?.path || `${baseDir}/${newName.trim()}`

            try {
              // Use vault API to copy file to new location and remove old one
              if (window.electronAPI?.vault?.copyFile && window.electronAPI?.vault?.removeFile) {
                const copyResult = await window.electronAPI.vault.copyFile(currentPath, newPath)
                if (copyResult.success) {
                  const removeResult = await window.electronAPI.vault.removeFile(currentPath)
                  if (removeResult.success) {
                    console.log('File renamed successfully:', currentPath, '->', newPath)
                    // Refresh the file list
                    if (selectedFolder) {
                      loadFolderFiles(selectedFolder)
                    }
                    // TODO: Show success toast
                  } else {
                    console.error('Error removing old file:', removeResult.error)
                    // TODO: Show error toast
                  }
                } else {
                  console.error('Error copying file:', copyResult.error)
                  // TODO: Show error toast
                }
              }
            } catch (error) {
              console.error('Error renaming file:', error)
              // TODO: Show error toast
            }
          }
        }
        break

      case 'properties':
        // TODO: Implement file properties dialog
        console.log('Show properties:', data)
        break

      default:
        // Pass through to main file action handler
        handleFileAction(action, data)
        break
    }
  }

  /**
   * Handle Smart Instruction processing
   */
  const handleSmartInstruction = async () => {
    if (!smartInstruction.trim()) {
      setInstructionResult({
        success: false,
        message: 'Please enter an instruction'
      })
      return
    }

    // Auto-select a context if none is selected (Master mode should always have a context)
    let contextId = selectedContextId
    if (!contextId) {
      console.log('🔍 [SMART-INSTRUCT-DEBUG] No context selected, attempting to auto-select...')
      console.log('🔍 [SMART-INSTRUCT-DEBUG] selectedContextId:', selectedContextId)
      console.log('🔍 [SMART-INSTRUCT-DEBUG] lastContextVaultId:', lastContextVaultId)

      // Try to use last context vault
      let targetContextId = lastContextVaultId

      // If no last context, fallback to personal vault's first context
      if (!targetContextId) {
        console.log('🔍 [SMART-INSTRUCT-DEBUG] No lastContextVaultId, checking vaults...')
        const vaults = contextVaultService.getCurrentVaults()
        console.log('🔍 [SMART-INSTRUCT-DEBUG] Available vaults:', vaults)

        const personalVault = vaults.find(v => v.name.toLowerCase().includes('personal'))
        console.log('🔍 [SMART-INSTRUCT-DEBUG] Personal vault found:', personalVault)

        if (personalVault && personalVault.contexts.length > 0) {
          targetContextId = personalVault.contexts[0].id
          console.log('🔍 [SMART-INSTRUCT-DEBUG] Auto-selected personal vault first context:', targetContextId)
        } else {
          // If no personal vault, use any first available context
          console.log('🔍 [SMART-INSTRUCT-DEBUG] No personal vault, checking all contexts...')
          const allContexts = contextVaultService.getAllContexts()
          console.log('🔍 [SMART-INSTRUCT-DEBUG] All available contexts:', allContexts)

          if (allContexts.length > 0) {
            targetContextId = allContexts[0].id
            console.log('🔍 [SMART-INSTRUCT-DEBUG] Auto-selected first available context:', targetContextId)
          } else {
            console.log('🔍 [SMART-INSTRUCT-DEBUG] ❌ NO CONTEXTS FOUND AT ALL!')
          }
        }
      }

      if (targetContextId) {
        // Set the context and update state
        contextVaultService.setSelectedContext(targetContextId)
        setSelectedContextId(targetContextId)
        contextId = targetContextId
        console.log('Auto-selected context for Smart Instruct:', targetContextId)
      } else {
        setInstructionResult({
          success: false,
          message: 'No context vault available. Please create a context vault first.'
        })
        return
      }
    }

    setIsProcessingInstruction(true)
    setInstructionResult(null)

    try {
      // Get current vault path using the resolved context ID
      console.log('🎯 [SMART-INSTRUCT-DEBUG] Attempting to find context by ID:', contextId)
      const contextResult = contextVaultService.findContextById(contextId)
      console.log('🎯 [SMART-INSTRUCT-DEBUG] Context result:', contextResult)

      if (!contextResult) {
        console.log('🎯 [SMART-INSTRUCT-DEBUG] ❌ Context not found for ID:', contextId)
        throw new Error('Selected context not found')
      }

      const currentVault = contextResult.vault
      console.log('🎯 [SMART-INSTRUCT-DEBUG] Using vault path:', currentVault)

                          // Create smart instruction request
                    const request: SmartInstructionRequest = {
                      instruction: smartInstruction.trim(),
                      filePath: selectedFile || 'master.md',
                      vaultPath: currentVault.path,
                      context: {
                        currentContent: masterContent,
                        userIntent: smartInstruction.trim()
                      }
                    }

                                        // Override model selection for smart instruction if specified
                    let response
                    if (smartInstructionModel) {
                      // Temporarily update the selected model for this instruction
                      const originalModel = useAppStore.getState().settings.selectedModel
                      useAppStore.getState().updateSettings({ selectedModel: smartInstructionModel })
                      
                      try {
                        response = await smartInstructionService.processSmartInstruction(request)
                        // Restore original model
                        useAppStore.getState().updateSettings({ selectedModel: originalModel })
                      } catch (error) {
                        // Restore original model on error
                        useAppStore.getState().updateSettings({ selectedModel: originalModel })
                        throw error
                      }
                    } else {
                      // Use default model selection logic
                      response = await smartInstructionService.processSmartInstruction(request)
                    }

      if (response.success) {
        // Update the master content
        setMasterContent(response.updatedContent)
        
        // Save the updated content to the file
        const filePath = `${currentVault.path}/master.md`
        await window.electronAPI.vault.writeFile(filePath, response.updatedContent)

                              setInstructionResult({
                        success: true,
                        message: `Smart instruction processed successfully! ${response.changes.sections.length + response.changes.additions.length + response.changes.modifications.length} changes made.`,
                        changes: [...response.changes.sections, ...response.changes.additions, ...response.changes.modifications],
                        metadata: response.metadata
                      })

        // Clear the instruction input
        setSmartInstruction('')
      } else {
        setInstructionResult({
          success: false,
          message: 'Failed to process smart instruction'
        })
      }
    } catch (error) {
      console.error('Error processing smart instruction:', error)
      setInstructionResult({
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    } finally {
      setIsProcessingInstruction(false)
    }
  }

  const renderFileTreeNode = (node: FileTreeNode, level: number = 0) => {
    const isExpanded = expandedFolders.has(node.path)
    const isSelected = selectedFile === node.path
    const marginLeft = level * 16 // 4 * 4 = 16px per level

    // Debug: Log expansion check
    if (node.type === 'folder') {
      console.log(`Checking expansion for ${node.name} (${node.path}): ${isExpanded}`)
      console.log('Available expanded paths:', Array.from(expandedFolders))
    }

    return (
      <div key={node.path}>
        <div
          className={`
            file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 transition-all relative
            ${isSelected ? 'selected bg-primary/20 border border-primary/30' : 'hover:bg-gray-700/50'}
            ${dragOver === node.path && node.type === 'folder' ? 'drop-target' : ''}
          `}
          style={{ marginLeft: `${marginLeft}px` }}
          onDragOver={node.type === 'folder' ? (e) => handleDragOver(e, node.path) : undefined}
          onDragLeave={node.type === 'folder' ? handleDragLeave : undefined}
          onDrop={node.type === 'folder' ? (e) => handleFileDrop(e, node.path) : undefined}
          onClick={() => {
            if (node.type === 'folder') {
              // Always expand the folder when clicked
              setExpandedFolders(prev => {
                const newSet = new Set(prev)
                newSet.add(node.path)
                return newSet
              })
              selectFolder(node.path)
            } else {
              selectFile(node.path)
            }
          }}
          onContextMenu={(e) => handleContextMenu(e, node.name, node.path)}
        >
          {node.type === 'folder' && (
            <FontAwesomeIcon
              icon={isExpanded ? ICONS.chevronDown : ICONS.chevronRight}
              className="text-gray-400 text-xs w-3"
            />
          )}
          {node.type === 'file' && <div className="w-3"></div>}

          <FontAwesomeIcon icon={node.icon || ICONS.file} className={`text-sm ${node.color}`} />
          
          <span className={`text-sm ${isSelected ? 'text-primary font-medium' : node.color === 'text-primary' ? 'text-primary font-medium' : 'text-supplement1'}`}>
            {node.name}
          </span>
          
          <div className="ml-auto">
            {/* File count badge would go here if available */}
            {isSelected && node.type === 'file' && (
              <div className="w-2 h-2 bg-primary rounded-full"></div>
            )}
          </div>
        </div>
        
        {node.type === 'folder' && isExpanded && node.children && (
          <div>
            {node.children.map(child => renderFileTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col h-full bg-gray-900 text-white">
      {/* Shared Dropbox header hidden per user request */}
      {/* {viewMode.currentMode === 'explorer' && (
        <div className="p-4">
          <h2 className="text-xl font-bold mb-4">Shared Dropbox</h2>

        </div>
      )} */}

      {/* Master.md view */}
      <div className="flex-1 flex bg-gray-900">
        
        {/* Left Column - Enhanced File Tree (Dynamic width based on file open state) */}
        <div className={`${isHubOpen ? 'w-1/5' : 'w-1/4'} bg-gray-800 border-r border-tertiary/50 flex flex-col transition-all duration-300`}>

          {/* Enhanced Vault Selector */}
          {useEnhancedSelector ? (
            <EnhancedVaultSelector
              selectedContextId={selectedContextId || undefined}
              onContextChange={setSelectedContextId}
            />
          ) : (
            <ContextVaultSelector
              selectedContextId={selectedContextId || undefined}
              onContextChange={setSelectedContextId}
            />
          )}

          {/* Quick Actions Toolbar */}
          <QuickActionsToolbar
            selectedContextId={selectedContextId || undefined}
            selectedFolder={selectedFolder || undefined}
            onFileUpload={handleFileUpload}
            onNewFile={handleNewFile}
            onNewFolder={handleNewFolder}
          />

          {/* File Search and Filter */}
          <FileSearchFilter
            onSearchChange={setSearchQuery}
            onFilterChange={setFileFilters}
            placeholder="Search files in vault..."
          />

          {/* View Toggle Buttons */}
          <div className="p-3 border-b border-tertiary/50">
            <div className="flex gap-2">
              <button 
                onClick={() => handleModeChange('explorer')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'explorer' 
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80' 
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <FontAwesomeIcon icon={ICONS.sitemap} className="text-sm" />
                <span className="text-xs font-medium">Explorer</span>
              </button>
              <button
                onClick={() => handleModeChange('master')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'master'
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80'
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <FontAwesomeIcon icon={ICONS.brain} className="text-sm" />
                <span className="text-xs font-medium">Master</span>
              </button>
            </div>
          </div>
          
          {/* File Tree */}
          <div className="flex-1 overflow-y-auto p-2">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <FontAwesomeIcon icon={ICONS.refresh} className="text-primary text-lg mb-2 animate-spin" />
                  <p className="text-sm text-supplement1">Loading files...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <p className="text-sm text-secondary mb-2">Error: {error}</p>
                  <button
                    onClick={loadFileTree}
                    className="px-3 py-1 bg-primary text-gray-900 rounded text-xs hover:bg-primary/80 transition-colors"
                  >
                    Retry
                  </button>
                </div>
              </div>
            ) : filteredFileTree.length > 0 ? (
              <>
                {searchQuery && (
                  <div className="mb-2 px-2 py-1 bg-gray-700/50 rounded text-xs text-gray-400">
                    {filteredFileTree.length} result{filteredFileTree.length !== 1 ? 's' : ''} for "{searchQuery}"
                  </div>
                )}
                {filteredFileTree.map(node => renderFileTreeNode(node))}
              </>
            ) : searchQuery ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <FontAwesomeIcon icon={ICONS.search} className="text-gray-400 text-lg mb-2" />
                  <p className="text-sm text-gray-400">No files found for "{searchQuery}"</p>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <FontAwesomeIcon icon={ICONS.folder} className="text-gray-400 text-lg mb-2" />
                  <p className="text-sm text-gray-400">No files in this vault</p>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Right Column Content - Conditional Hub or Explorer */}
        {(() => {
          if (process.env.NODE_ENV === 'development') {
            console.log('Rendering right column, viewMode.currentMode:', viewMode.currentMode)
            console.log('Master content length:', masterContent.length)
            console.log('Master loading:', masterLoading)
            console.log('Hub open:', isHubOpen)
          }

          // Master mode takes priority
          if (viewMode.currentMode === 'master') {
            return (
                              <MasterMode
                  content={masterContent}
                  loading={masterLoading}
                  selectedFile={selectedFile}
                  smartInstruction={smartInstruction}
                  isProcessingInstruction={isProcessingInstruction}
                  instructionResult={instructionResult}
                  onSmartInstructionChange={setSmartInstruction}
                  onSmartInstructionSubmit={handleSmartInstruction}
                  smartInstructionModel={smartInstructionModel}
                  onSmartInstructionModelChange={setSmartInstructionModel}
                  isModelSelectionOpen={isModelSelectionOpen}
                  onModelSelectionOpen={setIsModelSelectionOpen}
                  setSmartInstructionModel={setSmartInstructionModel}
                />
            )
          }



          // Show hub for supported files when open (legacy fallback)
          if (isHubOpen && selectedFile) {
            return (
              <div className="flex-1 flex">
                <FileOperationsHub
                  selectedFile={selectedFile}
                  filePath={selectedFile}
                  selectedContextId={selectedContextId || undefined}
                  onFileAction={handleFileAction}
                  onClose={closeHub}
                  onFileLoad={(content) => {
                    console.log('File content loaded:', content.length, 'characters')
                    // TODO: Store file content in state if needed
                  }}
                  onFileEdit={(content) => {
                    console.log('File edited:', selectedFile, content.length, 'characters')
                    // Optionally refresh file tree or update state
                  }}
                />
              </div>
            )
          }

          // Default to explorer mode
          return (
            <ExplorerMode
              selectedFolder={selectedFolder}
              folderFiles={folderFiles}
              folderLoading={folderLoading}
              onFileDoubleClick={handleFileDoubleClick}
              onFileRightClick={handleFileRightClick}
              onFolderNavigate={selectFolder}
            />
          )
        })()}
      </div>

      {/* Context Menu */}
      <FileContextMenu
        isOpen={contextMenu.isOpen}
        position={contextMenu.position}
        selectedFile={contextMenu.targetFile}
        filePath={contextMenu.targetPath}
        selectedContextId={selectedContextId || undefined}
        onClose={closeContextMenu}
        onAction={handleContextMenuAction}
      />

      {/* File Page Overlay */}
      {isPDFViewerOpen && (
        <FilePageOverlay onClose={closePDFViewer} />
      )}

      {/* Model Selection Overlay */}
      <ModelSelectionOverlay
        isOpen={isModelSelectionOpen}
        onClose={() => setIsModelSelectionOpen(false)}
        onModelSelect={setSmartInstructionModel}
        selectedModel={smartInstructionModel}
        title="Select Model for Smart Instruction"
      />
    </div>
  )
}

// Master Mode Component
interface MasterModeProps {
  content: string
  loading: boolean
  selectedFile: string | null
  smartInstruction: string
  isProcessingInstruction: boolean
  instructionResult: {
    success: boolean
    message: string
    changes?: string[]
    metadata?: {
      processingTime: number
      confidence: number
      modelUsed?: string
      isLocalModel: boolean
    }
  } | null
  onSmartInstructionChange: (value: string) => void
  onSmartInstructionSubmit: () => void
  smartInstructionModel: string
  onSmartInstructionModelChange: (modelId: string) => void
  isModelSelectionOpen: boolean
  onModelSelectionOpen: (isOpen: boolean) => void
  setSmartInstructionModel: (modelId: string) => void
}

const MasterMode: React.FC<MasterModeProps> = ({ 
  content, 
  loading, 
  selectedFile,
  smartInstruction,
  isProcessingInstruction,
  instructionResult,
  onSmartInstructionChange,
  onSmartInstructionSubmit,
  smartInstructionModel,
  onSmartInstructionModelChange,
  isModelSelectionOpen,
  onModelSelectionOpen,
  setSmartInstructionModel
}) => {
  // Extract title from content (first # heading)
  const getTitle = (content: string) => {
    const match = content.match(/^#\s+(.+)$/m)
    return match ? match[1] : 'Master Document'
  }

  // Simple markdown to HTML conversion for basic display
  const renderMarkdown = (content: string) => {
    if (!content) return ''

    return content
      .replace(/^# (.+)$/gm, '<h1 class="text-2xl font-bold text-supplement1 mb-4">$1</h1>')
      .replace(/^## (.+)$/gm, '<h2 class="text-xl font-semibold text-supplement1 mb-3 mt-6">$2</h2>')
      .replace(/^### (.+)$/gm, '<h3 class="text-lg font-medium text-supplement2 mb-2 mt-4">$3</h3>')
      .replace(/^\* (.+)$/gm, '<li class="text-gray-400 ml-4">• $1</li>')
      .replace(/^- (.+)$/gm, '<li class="text-gray-400 ml-4">• $1</li>')
      .replace(/\*\*(.+?)\*\*/g, '<strong class="text-supplement1">$1</strong>')
      .replace(/\*(.+?)\*/g, '<em class="text-gray-300">$1</em>')
      .replace(/`(.+?)`/g, '<code class="bg-gray-800 px-1 py-0.5 rounded text-primary text-sm">$1</code>')
      .replace(/\n\n/g, '</p><p class="text-gray-400 mb-4">')
      .replace(/^(?!<[h|l])/gm, '<p class="text-gray-400 mb-4">')
      .replace(/<p class="text-gray-400 mb-4">(<[h|l])/g, '$1')
  }

  return (
    <div className="flex-1 flex flex-col">
      
      {/* Top Section - Master.md Preview (60%) */}
      <div className="h-[60%] flex border-b border-tertiary/50">
        
        {/* Markdown Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <FontAwesomeIcon icon={ICONS.refresh} className="text-primary text-2xl mb-4 animate-spin" />
                <p className="text-supplement1">Loading master document...</p>
              </div>
            </div>
          ) : (
            <div className="markdown-content">
              {/* Document Header */}
              <div className="flex items-center gap-3 mb-6 pb-4 border-b border-tertiary/30">
                <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                  <FontAwesomeIcon icon={ICONS.fileText} className="text-primary text-lg" />
                </div>
                <div>
                  <h1 className="text-supplement1 text-xl font-semibold">{getTitle(content)}</h1>
                  <div className="flex items-center gap-4 text-xs text-gray-400 mt-1">
                    <span className="flex items-center gap-1">
                      <FontAwesomeIcon icon={ICONS.clock} className="text-xs" />
                      Just updated
                    </span>
                    <span className="flex items-center gap-1">
                      <FontAwesomeIcon icon={ICONS.fileText} className="text-xs" />
                      {selectedFile ? selectedFile.split('/').pop() : 'master.md'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Markdown Content */}
              <div
                className="prose prose-invert max-w-none"
                dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }}
              />
            </div>
          )}
        </div>

        {/* Context Management Sidebar */}
        <div className="w-80 bg-gray-800 border-l border-tertiary/50 p-4 flex flex-col">
          <div className="mb-4">
            <h4 className="font-medium text-supplement1 mb-2 text-sm">Context Management</h4>
          </div>
          
          <div className="space-y-2">
            <button className="w-full p-2 bg-primary/20 border border-primary/50 rounded-lg text-left hover:bg-primary/30 transition-colors">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon icon={ICONS.questionCircle} className="text-primary text-sm" />
                <div>
                  <p className="text-xs font-medium text-supplement1">Ask about this file</p>
                  <p className="text-xs text-gray-400">Get insights</p>
                </div>
              </div>
            </button>
            
            <button className="w-full p-2 bg-secondary/20 border border-secondary/50 rounded-lg text-left hover:bg-secondary/30 transition-colors">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon icon={ICONS.compress} className="text-secondary text-sm" />
                <div>
                  <p className="text-xs font-medium text-supplement1">Summarize</p>
                  <p className="text-xs text-gray-400">Brief summary</p>
                </div>
              </div>
            </button>
            
            <button className="w-full p-2 bg-supplement2/20 border border-supplement2/50 rounded-lg text-left hover:bg-supplement2/30 transition-colors">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon icon={ICONS.edit} className="text-supplement2 text-sm" />
                <div>
                  <p className="text-xs font-medium text-supplement1">Edit content</p>
                  <p className="text-xs text-gray-400">Make improvements</p>
                </div>
              </div>
            </button>
          </div>
          
          <div className="flex-1"></div>
          
          <div className="border-t border-tertiary/50 pt-4">
            {/* Instruction Result Display */}
            {instructionResult && (
              <div className={`mb-3 p-3 rounded-lg text-xs ${
                instructionResult.success 
                  ? 'bg-green-900/30 border border-green-500/50 text-green-300' 
                  : 'bg-red-900/30 border border-red-500/50 text-red-300'
              }`}>
                <div className="font-medium mb-1">
                  {instructionResult.success ? '✓ Success' : '✗ Error'}
                </div>
                <div className="text-gray-300">{instructionResult.message}</div>
                {instructionResult.changes && instructionResult.changes.length > 0 && (
                  <div className="mt-2">
                    <div className="font-medium text-green-300 mb-1">Changes made:</div>
                    <ul className="list-disc list-inside space-y-1">
                      {instructionResult.changes.map((change, index) => (
                        <li key={index} className="text-gray-300">{change}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {/* LLM Model Information */}
                {instructionResult.success && instructionResult.metadata && (
                  <div className="mt-2 pt-2 border-t border-green-500/30">
                    <div className="flex items-center gap-2 text-xs">
                      <FontAwesomeIcon 
                        icon={instructionResult.metadata.isLocalModel ? ICONS.shield : ICONS.cloud} 
                        className={instructionResult.metadata.isLocalModel ? 'text-green-400' : 'text-blue-400'}
                      />
                      <span className="text-gray-300">
                        {instructionResult.metadata.isLocalModel ? 'Local LLM' : 'External LLM'}: {instructionResult.metadata.modelUsed}
                      </span>
                    </div>
                    <div className="text-xs text-gray-400 mt-1">
                      Confidence: {Math.round(instructionResult.metadata.confidence * 100)}% | 
                      Time: {Math.round(instructionResult.metadata.processingTime)}ms
                    </div>
                  </div>
                )}
              </div>
            )}

            <textarea 
              value={smartInstruction}
              onChange={(e) => onSmartInstructionChange(e.target.value)}
              placeholder="Enter a smart instruction (e.g., 'Research API design patterns', 'Plan project structure', 'Review current progress')..." 
              className="w-full h-20 bg-gray-700 border border-tertiary/50 rounded-lg p-3 text-xs text-supplement1 placeholder-gray-400 resize-none focus:outline-none focus:border-primary/50"
              disabled={isProcessingInstruction}
            />
            
            {/* Smart Instruction Input and Controls */}
            <div className="flex gap-2 mt-3">
              {/* Gear Icon for Model Selection */}
              <button
                onClick={() => onModelSelectionOpen(true)}
                className="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
                title="Select model for Smart Instruction"
              >
                <FontAwesomeIcon icon={ICONS.cog} className="h-5 w-5 text-gray-300" />
              </button>
              
              {/* Submit Button */}
              <button 
                onClick={onSmartInstructionSubmit}
                disabled={isProcessingInstruction || !smartInstruction.trim()}
                className={`flex-1 p-2 rounded-lg font-medium text-sm transition-colors ${
                  isProcessingInstruction || !smartInstruction.trim()
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : 'bg-primary text-gray-900 hover:bg-primary/80'
                }`}
              >
                {isProcessingInstruction ? (
                  <div className="flex items-center justify-center gap-2">
                    <FontAwesomeIcon icon={ICONS.refresh} className="animate-spin text-sm" />
                    Processing...
                  </div>
                ) : (
                  'Smart Instruction'
                )}
              </button>
            </div>
            
            {/* Model Selection Info */}
            {smartInstructionModel && (
              <div className="mt-2 p-2 bg-blue-900/20 border border-blue-500/30 rounded-lg">
                <div className="flex items-center gap-2 text-xs">
                  <FontAwesomeIcon icon={ICONS.info} className="text-blue-400" />
                  <span className="text-blue-300">Using model: {smartInstructionModel}</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Bottom Section - Recent Activity (40%) */}
      <div className="h-[40%] bg-gray-850/70">
        
        {/* Folder Divider */}
        <div className="flex items-center justify-center py-1 cursor-pointer hover:bg-gray-700/50 transition-colors group">
          <div className="w-12 h-1 bg-gray-600 rounded-full group-hover:bg-primary/60 transition-colors"></div>
        </div>
        
        {/* Header */}
        <div className="p-4 border-b border-tertiary/50">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-supplement1">Recent Activity</h3>
            <button className="text-xs text-primary hover:text-primary/80 transition-colors">View All</button>
          </div>
        </div>
        
        {/* Two Column Layout */}
        <div className="flex h-full">
          
          {/* Recent Chats Column */}
          <div className="w-1/2 border-r border-tertiary/50">
            <div className="p-3 border-b border-tertiary/30">
              <h4 className="text-xs font-medium text-supplement1">Recent Chats</h4>
            </div>
            <div className="overflow-y-auto p-3 h-full">
              <div className="space-y-2">
                
                {/* Chat Item 1 */}
                <div className="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h5 className="font-medium text-xs text-supplement1 mb-1">Component Architecture Discussion</h5>
                      <p className="text-xs text-gray-400 line-clamp-2">How should we structure the component hierarchy for better maintainability?</p>
                    </div>
                    <div className="flex items-center gap-1 ml-2">
                      <span className="text-xs text-gray-500">2h ago</span>
                      <button className="p-1 hover:bg-gray-600 rounded transition-colors">
                        <FontAwesomeIcon icon={ICONS.reply} className="text-gray-400 text-xs" />
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-xs text-gray-400">12 messages</span>
                  </div>
                </div>
                
                {/* Chat Item 2 */}
                <div className="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h5 className="font-medium text-xs text-supplement1 mb-1">Design Token Updates</h5>
                      <p className="text-xs text-gray-400 line-clamp-2">We need to update the color tokens to match the new brand guidelines.</p>
                    </div>
                    <div className="flex items-center gap-1 ml-2">
                      <span className="text-xs text-gray-500">4h ago</span>
                      <button className="p-1 hover:bg-gray-600 rounded transition-colors">
                        <FontAwesomeIcon icon={ICONS.reply} className="text-gray-400 text-xs" />
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-secondary rounded-full"></div>
                    <span className="text-xs text-gray-400">8 messages</span>
                  </div>
                </div>
                
              </div>
            </div>
          </div>
          
          {/* Recent Files Column */}
          <div className="w-1/2">
            <div className="p-3 border-b border-tertiary/30">
              <h4 className="text-xs font-medium text-supplement1">Recent Files</h4>
            </div>
            <div className="overflow-y-auto p-3 h-full">
              <div className="space-y-2">
                
                {/* File Item 1 */}
                <div className="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2 flex-1">
                      <FontAwesomeIcon icon={ICONS.fileText} className="text-primary text-sm" />
                      <div className="flex-1">
                        <h5 className="font-medium text-xs text-supplement1 mb-1">master.md</h5>
                        <p className="text-xs text-gray-400">Project overview and getting started guide</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 ml-2">
                      <span className="text-xs text-gray-500">1h ago</span>
                      <button className="p-1 hover:bg-gray-600 rounded transition-colors">
                        <FontAwesomeIcon icon={ICONS.externalLink} className="text-gray-400 text-xs" />
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-xs text-gray-400">Modified</span>
                  </div>
                </div>
                
                {/* File Item 2 */}
                <div className="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2 flex-1">
                      <FontAwesomeIcon icon={ICONS.fileText} className="text-secondary text-sm" />
                      <div className="flex-1">
                        <h5 className="font-medium text-xs text-supplement1 mb-1">buttons.md</h5>
                        <p className="text-xs text-gray-400">Button component specifications and usage</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 ml-2">
                      <span className="text-xs text-gray-500">3h ago</span>
                      <button className="p-1 hover:bg-gray-600 rounded transition-colors">
                        <FontAwesomeIcon icon={ICONS.externalLink} className="text-gray-400 text-xs" />
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-secondary rounded-full"></div>
                    <span className="text-xs text-gray-400">Modified</span>
                  </div>
                </div>
                
              </div>
            </div>
          </div>
          
        </div>
      </div>

      {/* Model Selection Overlay */}
      <ModelSelectionOverlay
        isOpen={isModelSelectionOpen}
        onClose={() => onModelSelectionOpen(false)}
        onModelSelect={setSmartInstructionModel}
        selectedModel={smartInstructionModel}
        title="Select Model for Smart Instruction"
      />
    </div>
  )
}

// Explorer Mode Component
interface ExplorerModeProps {
  selectedFolder: string | null
  folderFiles: FileTreeNode[]
  folderLoading: boolean
  onFileDoubleClick?: (filePath: string) => void
  onFileRightClick?: (event: React.MouseEvent, filePath: string) => void
  onFolderNavigate?: (folderPath: string) => void
}

const ExplorerMode: React.FC<ExplorerModeProps> = ({
  selectedFolder,
  folderFiles,
  folderLoading,
  onFileDoubleClick,
  onFileRightClick,
  onFolderNavigate
}) => {
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')
  const [sortBy, setSortBy] = useState<'name' | 'modified' | 'type' | 'size'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set())

  // If no folder selected, show instruction
  if (!selectedFolder) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center max-w-md">
          <FontAwesomeIcon icon={ICONS.folder} className="text-supplement1 text-6xl mb-6" />
          <h2 className="text-2xl font-semibold text-supplement1 mb-4">Select a Folder</h2>
          <p className="text-gray-400 mb-8 leading-relaxed">
            Click on a folder in the tree view to explore its contents in the file explorer.
          </p>
        </div>
      </div>
    )
  }

  // Show loading state
  if (folderLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <FontAwesomeIcon icon={ICONS.refresh} className="text-primary text-3xl mb-4 animate-spin" />
          <p className="text-supplement1 text-lg">Loading folder contents...</p>
        </div>
      </div>
    )
  }

  // Use the real folder files from the selected folder
  const allFiles = folderFiles

  // Sort files
  const sortedFiles = [...allFiles].sort((a, b) => {
    let comparison = 0

    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name)
        break
      case 'modified':
        comparison = new Date(a.modified || 0).getTime() - new Date(b.modified || 0).getTime()
        break
      case 'type':
        const aType = a.type === 'folder' ? 'Folder' : getFileType(a.name)
        const bType = b.type === 'folder' ? 'Folder' : getFileType(b.name)
        comparison = aType.localeCompare(bType)
        break
      case 'size':
        comparison = (a.size || 0) - (b.size || 0)
        break
    }

    return sortOrder === 'asc' ? comparison : -comparison
  })

  const getFileType = (fileName: string): string => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'md': return 'Markdown File'
      case 'pdf': return 'PDF Document'
      case 'txt': return 'Text File'
      case 'docx': case 'doc': return 'Word Document'
      case 'xlsx': case 'xls': return 'Excel Spreadsheet'
      case 'pptx': case 'ppt': return 'PowerPoint Presentation'
      case 'png': case 'jpg': case 'jpeg': case 'gif': return 'Image File'
      case 'json': return 'JSON File'
      default: return 'File'
    }
  }

  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '-'
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  const formatDate = (dateString?: string): string => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)
    const diffWeeks = Math.floor(diffDays / 7)

    if (diffHours < 1) return 'Just now'
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
    if (diffWeeks < 4) return `${diffWeeks} week${diffWeeks > 1 ? 's' : ''} ago`
    return date.toLocaleDateString()
  }

  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  const toggleFileSelection = (filePath: string) => {
    const newSelection = new Set(selectedFiles)
    if (newSelection.has(filePath)) {
      newSelection.delete(filePath)
    } else {
      newSelection.add(filePath)
    }
    setSelectedFiles(newSelection)
  }

  const getSortIcon = (column: typeof sortBy) => {
    if (sortBy !== column) return ICONS.sort
    return sortOrder === 'asc' ? ICONS.sortUp : ICONS.sortDown
  }

  // Generate breadcrumb segments from folder path
  const getBreadcrumbs = () => {
    if (!selectedFolder) return []
    const segments = selectedFolder.split(/[\\/]/).filter(Boolean)
    const breadcrumbs = [] as Array<{ name: string; path: string }>
    let acc: string[] = []
    for (let i = 0; i < segments.length; i++) {
      acc.push(segments[i])
      const name = segments[i]
      breadcrumbs.push({ name, path: acc.join('/') })
    }
    return breadcrumbs
  }

  return (
    <div className="flex-1 bg-gray-900 flex flex-col">
      {/* Explorer Header */}
      <div className="border-b border-tertiary/50 p-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold text-supplement1">File Explorer</h2>
          <div className="flex items-center gap-2">
            {/* Breadcrumb Navigation */}
            {selectedFolder ? (
              <div className="flex items-center gap-1">
                {getBreadcrumbs().map((crumb, index) => (
                  <div key={crumb.path} className="flex items-center gap-1">
                    {index > 0 && (
                      <FontAwesomeIcon
                        icon={ICONS.chevronRight}
                        className="text-xs text-gray-500"
                      />
                    )}
                    <button
                      onClick={() => onFolderNavigate?.(crumb.path)}
                      className="text-sm text-gray-400 hover:text-primary transition-colors px-1 py-0.5 rounded hover:bg-gray-800"
                      title={`Navigate to ${crumb.path}`}
                    >
                      {crumb.name}
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <span className="text-sm text-gray-400">No folder selected</span>
            )}
            <span className="text-xs text-gray-500">•</span>
            <span className="text-sm text-gray-400">
              {allFiles.length} item{allFiles.length !== 1 ? 's' : ''}
            </span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'list'
                ? 'bg-primary/20 text-primary'
                : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
            }`}
          >
            <FontAwesomeIcon icon={ICONS.list} className="text-sm" />
          </button>
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'grid'
                ? 'bg-primary/20 text-primary'
                : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
            }`}
          >
            <FontAwesomeIcon icon={ICONS.thLarge} className="text-sm" />
          </button>
        </div>
      </div>

      {/* File Content */}
      <div className="flex-1 overflow-y-auto">
        {viewMode === 'list' ? (
          <table className="w-full">
            <thead className="bg-gray-800 border-b border-tertiary/50 sticky top-0">
              <tr>
                <th className="text-left p-4 text-sm font-medium text-supplement1">
                  <button
                    onClick={() => handleSort('name')}
                    className="flex items-center gap-2 hover:text-primary transition-colors"
                  >
                    Name
                    <FontAwesomeIcon icon={getSortIcon('name')} className="text-xs" />
                  </button>
                </th>
                <th className="text-left p-4 text-sm font-medium text-supplement1">
                  <button
                    onClick={() => handleSort('modified')}
                    className="flex items-center gap-2 hover:text-primary transition-colors"
                  >
                    Date Modified
                    <FontAwesomeIcon icon={getSortIcon('modified')} className="text-xs" />
                  </button>
                </th>
                <th className="text-left p-4 text-sm font-medium text-supplement1">
                  <button
                    onClick={() => handleSort('type')}
                    className="flex items-center gap-2 hover:text-primary transition-colors"
                  >
                    Type
                    <FontAwesomeIcon icon={getSortIcon('type')} className="text-xs" />
                  </button>
                </th>
                <th className="text-left p-4 text-sm font-medium text-supplement1">
                  <button
                    onClick={() => handleSort('size')}
                    className="flex items-center gap-2 hover:text-primary transition-colors"
                  >
                    Size
                    <FontAwesomeIcon icon={getSortIcon('size')} className="text-xs" />
                  </button>
                </th>
              </tr>
            </thead>
            <tbody>
              {sortedFiles.map((file) => (
                <tr
                  key={file.path}
                  className={`border-b border-gray-800 cursor-pointer transition-colors hover:bg-gray-800/50 ${
                    selectedFiles.has(file.path) ? 'bg-primary/10 border-l-2 border-l-primary' : ''
                  }`}
                  onClick={() => toggleFileSelection(file.path)}
                  onDoubleClick={() => onFileDoubleClick?.(file.path)}
                  onContextMenu={(e) => {
                    e.preventDefault()
                    onFileRightClick?.(e, file.path)
                  }}
                >
                  <td className="p-4">
                    <div className="flex items-center gap-3">
                      <FontAwesomeIcon
                        icon={file.type === 'folder' ? ICONS.folder : ICONS.fileLines}
                        className={`text-lg ${
                          file.type === 'folder' ? 'text-supplement2' : 'text-primary'
                        }`}
                      />
                      <span className="text-sm text-supplement1 font-medium">{file.name}</span>
                    </div>
                  </td>
                  <td className="p-4">
                    <span className="text-sm text-gray-400">{formatDate(file.modified)}</span>
                  </td>
                  <td className="p-4">
                    <span className="text-sm text-gray-400">
                      {file.type === 'folder' ? 'Folder' : getFileType(file.name)}
                    </span>
                  </td>
                  <td className="p-4">
                    <span className="text-sm text-gray-400">{formatFileSize(file.size)}</span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="p-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            {sortedFiles.map((file) => (
              <div
                key={file.path}
                className={`p-4 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 cursor-pointer transition-colors border ${
                  selectedFiles.has(file.path)
                    ? 'border-primary/50 bg-primary/10'
                    : 'border-transparent hover:border-primary/30'
                }`}
                onClick={() => toggleFileSelection(file.path)}
                onDoubleClick={() => onFileDoubleClick?.(file.path)}
                onContextMenu={(e) => {
                  e.preventDefault()
                  onFileRightClick?.(e, file.path)
                }}
              >
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-3">
                    <FontAwesomeIcon
                      icon={file.type === 'folder' ? ICONS.folder : ICONS.fileLines}
                      className={`text-xl ${
                        file.type === 'folder' ? 'text-supplement2' : 'text-primary'
                      }`}
                    />
                  </div>
                  <h4 className="text-sm font-medium text-supplement1 mb-1 truncate w-full">{file.name}</h4>
                  <p className="text-xs text-gray-400">{formatFileSize(file.size)} • {file.type === 'folder' ? 'Folder' : getFileType(file.name)}</p>
                  <p className="text-xs text-gray-500 mt-1">{formatDate(file.modified)}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default FilesPage
