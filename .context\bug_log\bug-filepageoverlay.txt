FilePageOverlay.tsx:1108 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:1109 [LABELS] 🎯 OVERLAY: fileIntelligence: null
FilePageOverlay.tsx:1110 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 0, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
FilePageOverlay.tsx:977 [LABELS] 🔄 OVERLAY: File opened, SmartLabelingInterface will manage intelligence state
FilePageOverlay.tsx:982 [LABELS] 🔄 OVERLAY: Loading states reset, intelligence state preserved
FilePageOverlay.tsx:1108 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:1109 [LABELS] 🎯 OVERLAY: fileIntelligence: null
FilePageOverlay.tsx:1110 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 0, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
FilePageOverlay.tsx:1108 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:1109 [LABELS] 🎯 OVERLAY: fileIntelligence: null
FilePageOverlay.tsx:1110 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:55 [LABELS] 🔄 SmartLabelingInterface: useEffect triggered
SmartLabelingInterface.tsx:56 [LABELS] 🔄 SmartLabelingInterface: filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
SmartLabelingInterface.tsx:57 [LABELS] 🔄 SmartLabelingInterface: fileContent length: 12858
SmartLabelingInterface.tsx:58 [LABELS] 🔄 SmartLabelingInterface: onLabelsChanged callback: true
SmartLabelingInterface.tsx:59 [LABELS] 🔄 SmartLabelingInterface: Starting intelligence loading process (persisted first)
SmartLabelingInterface.tsx:149 [LABELS] 🔍 KERNEL-STATUS: Checking kernel availability
SmartLabelingInterface.tsx:191 [LABELS] 🔍 KERNEL-STATUS: Checking if kernel has intelligence for file
SmartLabelingInterface.tsx:192 [LABELS] 🔍 KERNEL-STATUS: filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
SmartLabelingInterface.tsx:127 [LABELS] 🧹 Clearing any corrupted intelligence data for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
SmartLabelingInterface.tsx:138 [LABELS] 🧹 Intelligence state cleared, ready for fresh analysis
SmartLabelingInterface.tsx:272 [LABELS] 🔄 SmartLabelingInterface: loadOrProcessFileIntelligence called {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858}
SmartLabelingInterface.tsx:279 [LABELS] 🔄 KERNEL: Querying kernel for existing intelligence
SmartLabelingInterface.tsx:233 [LABELS] 🔍 KERNEL: Querying for existing sessions: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:205 [LABELS] 🔍 KERNEL-STATUS: Kernel response: {success: true, hasData: false, ideasCount: 0, hasIntelligence: false}
SmartLabelingInterface.tsx:165 [LABELS] 🔍 KERNEL-STATUS: Kernel status: {available: true, hasAnalyze: true, hasGet: true, hasIntelligence: false, electronAPI: true, …}
SmartLabelingInterface.tsx:174 [LABELS] 🔍 KERNEL-STATUS: Kernel available but no existing intelligence found
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/Feature-shared-dropbox.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/Feature-shared-dropbox.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
SmartLabelingInterface.tsx:244 [LABELS] 🔍 KERNEL: Using contextPath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
SmartLabelingInterface.tsx:253 [LABELS] 🔍 KERNEL: ℹ️ Kernel returned no intelligence: no data
SmartLabelingInterface.tsx:309 [LABELS] 🔄 SmartLabelingInterface: Setting idle state - no existing intelligence
SmartLabelingInterface.tsx:313 [LABELS] 🏷️ SmartLabelingInterface: Notifying overlay with empty ideas array (no existing intelligence)
SmartLabelingInterface.tsx:323 [LABELS] ℹ️ SmartLabelingInterface: No stored intelligence found. Waiting for user action to analyze...
SmartLabelingInterface.tsx:315 [LABELS] 🏷️ Calling onLabelsChanged with empty array
FilePageOverlay.tsx:183 [LABELS] 🎯 OVERLAY: handleLabelsChanged called with 0 ideas
FilePageOverlay.tsx:184 [LABELS] 🎯 OVERLAY: Received ideas: []
FilePageOverlay.tsx:191 [LABELS] 🎯 OVERLAY: Current fileIntelligence state: null
FilePageOverlay.tsx:210 [LABELS] 🎯 OVERLAY: Setting fileIntelligence with 0 ideas
FilePageOverlay.tsx:221 [LABELS] 🎯 OVERLAY: ✅ Updated fileIntelligence from SmartLabelingInterface: {totalIdeas: 0, selectedIdeas: 0, fileIntelligenceSet: true, hasIdeas: false}
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/Feature-shared-dropbox.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/Feature-shared-dropbox.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
SmartLabelingInterface.tsx:317 [LABELS] 🏷️ onLabelsChanged with empty array completed
FilePageOverlay.tsx:272 ✅ [INTELLIGENCE] Persisted labels and notes
FilePageOverlay.tsx:1108 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:1109 [LABELS] 🎯 OVERLAY: fileIntelligence: null
FilePageOverlay.tsx:1110 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
FilePageOverlay.tsx:1108 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:1109 [LABELS] 🎯 OVERLAY: fileIntelligence: exists
FilePageOverlay.tsx:1110 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
FilePageOverlay.tsx:1112 [LABELS] 🎯 OVERLAY: Ideas to display: []
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:403 [LABELS] 🤖 processFileIntelligence: Starting local LLM analysis
SmartLabelingInterface.tsx:404 [LABELS] 🤖 filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
SmartLabelingInterface.tsx:405 [LABELS] 🤖 fileContent length: 12858
SmartLabelingInterface.tsx:418 [LABELS] 🤖 LOCAL-LLM: Checking for available local models
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:421 [LABELS] 🤖 Provider Status: {ollama: {…}, lmstudio: {…}}
SmartLabelingInterface.tsx:446 [LABELS] 🤖 LOCAL-LLM: Using model: gemma3:4b
SmartLabelingInterface.tsx:452 [LABELS] 🤖 📡 Sending request to local model: ollama:gemma3:4b
SmartLabelingInterface.tsx:453 [LABELS] 🤖 📡 Prompt preview: You are an expert document analyzer. Extract key ideas and labels from the following document content.

REQUIREMENTS:
- Extract at least 1 meaningful label/key idea (minimum requirement)
- Focus on the most important topics, themes, and concepts
- Each label should be 1-4 words maximum
- Labels shou...
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'processing', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: processing
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:468 [LABELS] 🤖 📡 Raw response received: {
  "key_ideas": [
    {
      "id": "1",
      "text": "Shared Dropbox Restoration",
      "relevance_score": 95,
      "intent_types": ["outcome", "status"],
      "weight": 1.0,
      "auto_selected": true,
      "user_confirmed": true,
      "context": "The primary goal of the analysis was to successfully restore the shared dropbox functionality."
    },
    {
      "id": "2",
      "text": "Context Vault Selection",
      "relevance_score": 90,
      "intent_types": ["technical", "feature"]...
SmartLabelingInterface.tsx:913 [LABELS] 🤖 📝 Parsing LLM response...
SmartLabelingInterface.tsx:932 [LABELS] 🤖 📝 Attempting to parse JSON: {
  "key_ideas": [
    {
      "id": "1",
      "text": "Shared Dropbox Restoration",
      "relevance_score": 95,
      "intent_types": ["outcome", "status"],
      "weight": 1.0,
      "auto_selecte...
SmartLabelingInterface.tsx:948 [LABELS] 🤖 📝 ✅ Successfully parsed 3 ideas from LLM response
SmartLabelingInterface.tsx:480 [LABELS] 🤖 LOCAL-LLM: ✅ Analysis successful, extracted 3 ideas
FilePageOverlay.tsx:183 [LABELS] 🎯 OVERLAY: handleLabelsChanged called with 3 ideas
FilePageOverlay.tsx:184 [LABELS] 🎯 OVERLAY: Received ideas: (3) [{…}, {…}, {…}]
FilePageOverlay.tsx:191 [LABELS] 🎯 OVERLAY: Current fileIntelligence state: exists
FilePageOverlay.tsx:210 [LABELS] 🎯 OVERLAY: Setting fileIntelligence with 3 ideas
FilePageOverlay.tsx:221 [LABELS] 🎯 OVERLAY: ✅ Updated fileIntelligence from SmartLabelingInterface: {totalIdeas: 3, selectedIdeas: 3, fileIntelligenceSet: true, hasIdeas: true}
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/Feature-shared-dropbox.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/Feature-shared-dropbox.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
SmartLabelingInterface.tsx:506 [LABELS] 🤖 LOCAL-LLM: Analysis completed successfully: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', ideasCount: 3, modelUsed: 'gemma3:4b', processingTime: '15427ms'}
FilePageOverlay.tsx:1108 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:1109 [LABELS] 🎯 OVERLAY: fileIntelligence: exists
FilePageOverlay.tsx:1110 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 3
FilePageOverlay.tsx:1112 [LABELS] 🎯 OVERLAY: Ideas to display: (3) [{…}, {…}, {…}]
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 3, selected_idea_ids_count: 3, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 3
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 3, others: 0, total: 3}
FilePageOverlay.tsx:272 ✅ [INTELLIGENCE] Persisted labels and notes
FilePageOverlay.tsx:1108 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:1109 [LABELS] 🎯 OVERLAY: fileIntelligence: exists
FilePageOverlay.tsx:1110 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 3
FilePageOverlay.tsx:1112 [LABELS] 🎯 OVERLAY: Ideas to display: (3) [{…}, {…}, {…}]
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 3, selected_idea_ids_count: 3, processing_status: 'complete', show_all_ideas: false}available_ideas_count: 3processing_status: "complete"selected_idea_ids_count: 3show_all_ideas: false[[Prototype]]: Objectconstructor: ƒ Object()assign: ƒ assign()create: ƒ create()defineProperties: ƒ defineProperties()defineProperty: ƒ defineProperty()entries: ƒ entries()freeze: ƒ freeze()fromEntries: ƒ fromEntries()getOwnPropertyDescriptor: ƒ getOwnPropertyDescriptor()getOwnPropertyDescriptors: ƒ getOwnPropertyDescriptors()getOwnPropertyNames: ƒ getOwnPropertyNames()getOwnPropertySymbols: ƒ getOwnPropertySymbols()getPrototypeOf: ƒ getPrototypeOf()groupBy: ƒ groupBy()hasOwn: ƒ hasOwn()is: ƒ is()isExtensible: ƒ isExtensible()isFrozen: ƒ isFrozen()isSealed: ƒ isSealed()keys: ƒ keys()length: 1name: "Object"preventExtensions: ƒ preventExtensions()prototype: {__defineGetter__: ƒ, __defineSetter__: ƒ, hasOwnProperty: ƒ, __lookupGetter__: ƒ, __lookupSetter__: ƒ, …}seal: ƒ seal()setPrototypeOf: ƒ setPrototypeOf()values: ƒ values()arguments: (...)caller: (...)[[Prototype]]: ƒ ()[[Scopes]]: Scopes[0]hasOwnProperty: ƒ hasOwnProperty()isPrototypeOf: ƒ isPrototypeOf()propertyIsEnumerable: ƒ propertyIsEnumerable()toLocaleString: ƒ toLocaleString()toString: ƒ toString()valueOf: ƒ valueOf()__defineGetter__: ƒ __defineGetter__()__defineSetter__: ƒ __defineSetter__()__lookupGetter__: ƒ __lookupGetter__()__lookupSetter__: ƒ __lookupSetter__()__proto__: (...)get __proto__: ƒ __proto__()length: 0name: "get __proto__"arguments: (...)caller: (...)[[Prototype]]: ƒ ()[[Scopes]]: Scopes[0]set __proto__: ƒ __proto__()length: 1name: "set __proto__"arguments: (...)caller: (...)[[Prototype]]: ƒ ()[[Scopes]]: Scopes[0]
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 3
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 3, others: 0, total: 3}autoSelected: 3others: 0total: 3[[Prototype]]: Objectconstructor: ƒ Object()hasOwnProperty: ƒ hasOwnProperty()isPrototypeOf: ƒ isPrototypeOf()propertyIsEnumerable: ƒ propertyIsEnumerable()toLocaleString: ƒ toLocaleString()toString: ƒ toString()valueOf: ƒ valueOf()__defineGetter__: ƒ __defineGetter__()__defineSetter__: ƒ __defineSetter__()__lookupGetter__: ƒ __lookupGetter__()__lookupSetter__: ƒ __lookupSetter__()__proto__: (...)get __proto__: ƒ __proto__()set __proto__: ƒ __proto__()
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 3, selected_idea_ids_count: 3, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 3
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 3, others: 0, total: 3}
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 3, selected_idea_ids_count: 3, processing_status: 'complete', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 3
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: complete
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 3, others: 0, total: 3}
FilePageOverlay.tsx:1108 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:1109 [LABELS] 🎯 OVERLAY: fileIntelligence: null
FilePageOverlay.tsx:1110 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 0, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
FilePageOverlay.tsx:977 [LABELS] 🔄 OVERLAY: File opened, SmartLabelingInterface will manage intelligence state
FilePageOverlay.tsx:982 [LABELS] 🔄 OVERLAY: Loading states reset, intelligence state preserved
FilePageOverlay.tsx:1108 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:1109 [LABELS] 🎯 OVERLAY: fileIntelligence: null
FilePageOverlay.tsx:1110 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 0, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
FilePageOverlay.tsx:1108 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:1109 [LABELS] 🎯 OVERLAY: fileIntelligence: null
FilePageOverlay.tsx:1110 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:55 [LABELS] 🔄 SmartLabelingInterface: useEffect triggered
SmartLabelingInterface.tsx:56 [LABELS] 🔄 SmartLabelingInterface: filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
SmartLabelingInterface.tsx:57 [LABELS] 🔄 SmartLabelingInterface: fileContent length: 12858
SmartLabelingInterface.tsx:58 [LABELS] 🔄 SmartLabelingInterface: onLabelsChanged callback: true
SmartLabelingInterface.tsx:59 [LABELS] 🔄 SmartLabelingInterface: Starting intelligence loading process (persisted first)
SmartLabelingInterface.tsx:149 [LABELS] 🔍 KERNEL-STATUS: Checking kernel availability
SmartLabelingInterface.tsx:191 [LABELS] 🔍 KERNEL-STATUS: Checking if kernel has intelligence for file
SmartLabelingInterface.tsx:192 [LABELS] 🔍 KERNEL-STATUS: filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
SmartLabelingInterface.tsx:127 [LABELS] 🧹 Clearing any corrupted intelligence data for: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
SmartLabelingInterface.tsx:138 [LABELS] 🧹 Intelligence state cleared, ready for fresh analysis
SmartLabelingInterface.tsx:272 [LABELS] 🔄 SmartLabelingInterface: loadOrProcessFileIntelligence called {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858}
SmartLabelingInterface.tsx:279 [LABELS] 🔄 KERNEL: Querying kernel for existing intelligence
SmartLabelingInterface.tsx:233 [LABELS] 🔍 KERNEL: Querying for existing sessions: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
SmartLabelingInterface.tsx:205 [LABELS] 🔍 KERNEL-STATUS: Kernel response: {success: true, hasData: false, ideasCount: 0, hasIntelligence: false}
SmartLabelingInterface.tsx:165 [LABELS] 🔍 KERNEL-STATUS: Kernel status: {available: true, hasAnalyze: true, hasGet: true, hasIntelligence: false, electronAPI: true, …}
SmartLabelingInterface.tsx:174 [LABELS] 🔍 KERNEL-STATUS: Kernel available but no existing intelligence found
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/Feature-shared-dropbox.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/Feature-shared-dropbox.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
SmartLabelingInterface.tsx:244 [LABELS] 🔍 KERNEL: Using contextPath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:253 [LABELS] 🔍 KERNEL: ℹ️ Kernel returned no intelligence: no data
SmartLabelingInterface.tsx:309 [LABELS] 🔄 SmartLabelingInterface: Setting idle state - no existing intelligence
SmartLabelingInterface.tsx:313 [LABELS] 🏷️ SmartLabelingInterface: Notifying overlay with empty ideas array (no existing intelligence)
SmartLabelingInterface.tsx:323 [LABELS] ℹ️ SmartLabelingInterface: No stored intelligence found. Waiting for user action to analyze...
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
SmartLabelingInterface.tsx:315 [LABELS] 🏷️ Calling onLabelsChanged with empty array
FilePageOverlay.tsx:183 [LABELS] 🎯 OVERLAY: handleLabelsChanged called with 0 ideas
FilePageOverlay.tsx:184 [LABELS] 🎯 OVERLAY: Received ideas: []
FilePageOverlay.tsx:191 [LABELS] 🎯 OVERLAY: Current fileIntelligence state: null
FilePageOverlay.tsx:210 [LABELS] 🎯 OVERLAY: Setting fileIntelligence with 0 ideas
FilePageOverlay.tsx:221 [LABELS] 🎯 OVERLAY: ✅ Updated fileIntelligence from SmartLabelingInterface: {totalIdeas: 0, selectedIdeas: 0, fileIntelligenceSet: true, hasIdeas: false}
vaultPath.ts:29 [LABELS] 🗂️ extractContextPath called with: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\Feature-shared-dropbox.md
vaultPath.ts:38 [LABELS] 🗂️ Normalized to posix: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/Feature-shared-dropbox.md
vaultPath.ts:45 [LABELS] 🗂️ Looking for token: /documents/ in posix path: C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started/documents/Feature-shared-dropbox.md found at index: 70
vaultPath.ts:49 [LABELS] 🗂️ ✅ Found context path using strategy 1 (subdir: documents ): C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
SmartLabelingInterface.tsx:317 [LABELS] 🏷️ onLabelsChanged with empty array completed
FilePageOverlay.tsx:1108 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:1109 [LABELS] 🎯 OVERLAY: fileIntelligence: exists
FilePageOverlay.tsx:1110 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
FilePageOverlay.tsx:1112 [LABELS] 🎯 OVERLAY: Ideas to display: []
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
FilePageOverlay.tsx:272 ✅ [INTELLIGENCE] Persisted labels and notes
FilePageOverlay.tsx:1108 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:1109 [LABELS] 🎯 OVERLAY: fileIntelligence: exists
FilePageOverlay.tsx:1110 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
FilePageOverlay.tsx:1112 [LABELS] 🎯 OVERLAY: Ideas to display: []
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
FilePageOverlay.tsx:1108 [LABELS] 🎯 OVERLAY: Rendering Key Ideas section
FilePageOverlay.tsx:1109 [LABELS] 🎯 OVERLAY: fileIntelligence: exists
FilePageOverlay.tsx:1110 [LABELS] 🎯 OVERLAY: fileIntelligence.key_ideas.length: 0
FilePageOverlay.tsx:1112 [LABELS] 🎯 OVERLAY: Ideas to display: []
SmartLabelingInterface.tsx:31 [LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with: {filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-v…tting-started\\documents\\Feature-shared-dropbox.md', fileContentLength: 12858, hasOnLabelsChanged: true, hasOnProcessingComplete: true}
SmartLabelingInterface.tsx:709 [LABELS] 🎨 SmartLabelingInterface render called
SmartLabelingInterface.tsx:710 [LABELS] 🎨 Current labelState: {available_ideas_count: 0, selected_idea_ids_count: 0, processing_status: 'idle', show_all_ideas: false}
SmartLabelingInterface.tsx:683 [LABELS] 🎨 getDisplayIdeas called
SmartLabelingInterface.tsx:684 [LABELS] 🎨 labelState.available_ideas.length: 0
SmartLabelingInterface.tsx:685 [LABELS] 🎨 labelState.show_all_ideas: false
SmartLabelingInterface.tsx:686 [LABELS] 🎨 labelState.processing_status: idle
SmartLabelingInterface.tsx:700 [LABELS] 🎨 Returning filtered ideas: {autoSelected: 0, others: 0, total: 0}
