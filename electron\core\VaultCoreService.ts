import fs from 'fs'
import path from 'path'
import { PathResolver } from './PathResolver'
import { EventBus } from './EventBus'

export class VaultCoreService {
  private watchers: Map<string, fs.FSWatcher> = new Map()

  async ensureContextFilesDir(vaultPath: string): Promise<string> {
    const filesDir = PathResolver.getContextFilesDir(vaultPath)
    await fs.promises.mkdir(filesDir, { recursive: true })
    return filesDir
  }

  async writeTextFile(filePath: string, content: string): Promise<void> {
    await fs.promises.writeFile(filePath, content, 'utf8')
  }

  async readTextFile(filePath: string): Promise<string> {
    return fs.promises.readFile(filePath, 'utf8')
  }

  async exists(filePath: string): Promise<boolean> {
    try {
      await fs.promises.access(filePath)
      return true
    } catch {
      return false
    }
  }

  // --- Minimal CRUD/scan helpers ---
  async readDirectory(dirPath: string): Promise<Array<{ name: string; path: string; isDirectory: boolean; size?: number; modified: string }>> {
    const entries = await fs.promises.readdir(dirPath, { withFileTypes: true })
    const results: Array<{ name: string; path: string; isDirectory: boolean; size?: number; modified: string }> = []
    for (const entry of entries) {
      const fullPath = PathResolver.joinSafe(dirPath, entry.name)
      const stat = await fs.promises.stat(fullPath)
      results.push({
        name: entry.name,
        path: fullPath,
        isDirectory: entry.isDirectory(),
        size: entry.isDirectory() ? undefined : stat.size,
        modified: stat.mtime.toISOString()
      })
    }
    return results
  }

  async copyFile(sourcePath: string, destinationPath: string): Promise<void> {
    await fs.promises.mkdir(path.dirname(destinationPath), { recursive: true })
    await fs.promises.copyFile(sourcePath, destinationPath)
  }

  async removeFile(targetPath: string): Promise<void> {
    await fs.promises.unlink(targetPath)
  }

  async removeDirectory(targetPath: string): Promise<void> {
    await fs.promises.rm(targetPath, { recursive: true, force: true })
  }

  async pathExists(targetPath: string): Promise<boolean> {
    try {
      await fs.promises.access(targetPath)
      return true
    } catch {
      return false
    }
  }

  // --- File system change detection ---
  startWatchingVault(vaultPath: string): void {
    const root = PathResolver.normalizePath(vaultPath)
    if (this.watchers.has(root)) return

    const watcher = fs.watch(root, { recursive: true }, async (eventType, filename) => {
      if (!filename) return
      try {
        const fullPath = PathResolver.joinSafe(root, filename)
        const exists = await this.pathExists(fullPath)
        if (exists) {
          const stat = await fs.promises.stat(fullPath).catch(() => null)
          EventBus.broadcast('file:changed', {
            path: PathResolver.normalizePath(fullPath),
            vaultPath: root,
            size: stat?.size,
            modified: stat?.mtime?.toISOString?.(),
            changeType: eventType === 'rename' ? 'added' : 'changed'
          })
        } else {
          EventBus.broadcast('file:removed', {
            path: PathResolver.normalizePath(fullPath),
            vaultPath: root,
            changeType: 'removed'
          })
        }
      } catch {}
    })

    this.watchers.set(root, watcher)
  }

  stopWatchingVault(vaultPath: string): void {
    const root = PathResolver.normalizePath(vaultPath)
    const watcher = this.watchers.get(root)
    if (watcher) {
      watcher.close()
      this.watchers.delete(root)
    }
  }
}
