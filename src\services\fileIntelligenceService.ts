import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { localModelService } from './localModelService'
import { cacheManager } from './cacheManager'
import {
  FileIntelligenceData,
  EntitySelection,
  SmartAnnotation,
  FileInteraction
} from '../types/intelligenceTypes'
import {
  FileIntelligence,
  VaultIntelligence,
  KeyIdea,
  WeightedEntity,
  HumanConnection,
  FileIntelligenceConfig,
  ProcessingProgress,
  ProcessingResult,
  BatchProcessingStatus,
  IntentType,
  EntityType,
  PriorityLevel,
  DEFAULT_FILE_INTELLIGENCE_CONFIG,
  PRIORITY_WEIGHTS,
  ENTITY_PRIORITY_MAP
} from '../types/fileIntelligenceTypes'

import crypto from 'crypto'
import { intelligence as intelligenceClient } from '../api/UnifiedAPIClient'

/**
 * File Intelligence Service
 * Comprehensive service for intelligent document analysis using local models
 * Manages both legacy intelligence data and new file intelligence processing
 */
class FileIntelligenceService extends BaseService {
  private readonly INTELLIGENCE_FOLDER = '.intelligence'
  private readonly CONTEXT_FOLDER = '.context'
  private readonly FILES_FOLDER = 'files'
  private processingQueue: Map<string, ProcessingProgress> = new Map()
  private batchStatus: BatchProcessingStatus | null = null

  constructor() {
    super({
      name: 'FileIntelligenceService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    this.logger.info('File Intelligence Service initialized', 'doInitialize')
  }

  // ============================================================================
  // NEW FILE INTELLIGENCE PROCESSING METHODS
  // ============================================================================

  /**
   * Main entry point: Analyze a single file and generate intelligence
   */
  async analyzeFile(
    filePath: string,
    content: string,
    config: Partial<FileIntelligenceConfig> = {}
  ): Promise<ProcessingResult> {
    const fullConfig = { ...DEFAULT_FILE_INTELLIGENCE_CONFIG, ...config }
    const startTime = Date.now()

    return await this.executeOperationOrThrow(
      'analyzeFile',
      async () => {
        // Generate cache key based on file path and content hash
        const contentHash = crypto.createHash('sha256').update(content).digest('hex').substring(0, 16)
        const cacheKey = `file_intelligence_${this.generateDocumentHash(filePath)}_${contentHash}`

        // Check cache first
        const cachedResult = await cacheManager.get<ProcessingResult>(cacheKey)
        if (cachedResult) {
          console.log(`🔥 [INTELLIGENCE] Cache hit for file: ${filePath}`)
          this.updateProgress(filePath, 'complete', 100, 'Loaded from cache')
          return cachedResult
        }

        this.logger.info('Starting file analysis', 'analyzeFile', {
          filePath,
          contentLength: content.length,
          model: fullConfig.local_model_preferred,
          cacheKey
        })

        // Update progress
        this.updateProgress(filePath, 'extracting', 10, 'Extracting content structure')

        // 1. Extract basic file metadata and structure
        const fileMetadata = await this.extractFileMetadata(filePath, content)

        // Update progress
        this.updateProgress(filePath, 'analyzing', 30, 'Analyzing with local model')

        // 2. Use local model to extract key ideas (10+ minimum)
        const keyIdeas = await this.extractKeyIdeasWithLocalModel(content, fullConfig)

        // DEBUG: Track key ideas extraction for hash consistency
        console.log('[LABELS] 🔍 HASH-DEBUG: Key ideas extracted from analysis')
        console.log('[LABELS] 🔍 HASH-DEBUG: File path:', filePath)
        console.log('[LABELS] 🔍 HASH-DEBUG: Key ideas count:', keyIdeas.length)
        console.log('[LABELS] 🔍 HASH-DEBUG: Key ideas preview:', keyIdeas.slice(0, 3).map(i => ({
          text: i.text,
          score: i.relevance_score,
          auto_selected: i.auto_selected
        })))

        // Update progress
        this.updateProgress(filePath, 'analyzing', 60, 'Extracting entities and connections')

        // 3. Extract weighted entities with human connection priority
        const weightedEntities = await this.extractWeightedEntities(content, keyIdeas)

        // 4. Extract human connections (highest priority)
        const humanConnections = await this.extractHumanConnections(content, weightedEntities)

        // Update progress
        this.updateProgress(filePath, 'storing', 90, 'Generating intelligence data')

        // 5. Build file intelligence structure
        const intelligence: FileIntelligence = {
          file_path: filePath,
          key_ideas: keyIdeas,
          weighted_entities: weightedEntities,
          human_connections: humanConnections,
          processing_confidence: this.calculateOverallConfidence(keyIdeas, weightedEntities),
          analysis_metadata: {
            processing_time_ms: Date.now() - startTime,
            model_used: fullConfig.local_model_preferred,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }

        // DEBUG: Track final intelligence object before storage
        console.log('[LABELS] 🔍 HASH-DEBUG: Final intelligence object ready for storage')
        console.log('[LABELS] 🔍 HASH-DEBUG: Intelligence file_path:', intelligence.file_path)
        console.log('[LABELS] 🔍 HASH-DEBUG: Intelligence key_ideas count:', intelligence.key_ideas.length)
        console.log('[LABELS] 🔍 HASH-DEBUG: Intelligence created_at:', intelligence.created_at)

        // 6. Store intelligence data
        await this.storeFileIntelligence(filePath, intelligence)

        // Update progress to complete
        this.updateProgress(filePath, 'complete', 100, 'Analysis complete')

        const processingTime = Date.now() - startTime

        const result: ProcessingResult = {
          success: true,
          file_path: filePath,
          processing_time_ms: processingTime,
          ideas_extracted: keyIdeas.length,
          entities_found: weightedEntities.length,
          human_connections: humanConnections.length,
          confidence_score: intelligence.processing_confidence,
          local_model_used: fullConfig.local_model_preferred
        }

        // Cache the result for future use
        await cacheManager.set(cacheKey, result)
        console.log(`💾 [INTELLIGENCE] Cached analysis result for: ${filePath}`)

        this.logger.info('File analysis completed', 'analyzeFile', result)
        return result
      },
      { filePath, contentLength: content.length }
    )
  }

  /**
   * Generate document hash for consistent file identification
   */
  private generateDocumentHash(filePath: string): string {
    return crypto.createHash('sha256').update(filePath).digest('hex').substring(0, 16)
  }

  /**
   * Extract key ideas using local model (minimum 10+ ideas)
   */
  private async extractKeyIdeasWithLocalModel(
    content: string,
    config: FileIntelligenceConfig
  ): Promise<KeyIdea[]> {
    try {
      // Check if local model is available
      const localModels = await localModelService.getAllLocalModels()
      const preferredModel = localModels.find(m => m.id === config.local_model_preferred)

      if (!preferredModel && !config.fallback_to_keyword_extraction) {
        throw new ServiceError(
          ServiceErrorCode.EXTERNAL_SERVICE_ERROR,
          `Preferred local model ${config.local_model_preferred} not available`,
          { serviceName: this.serviceName, operation: 'extractKeyIdeasWithLocalModel' }
        )
      }

      if (preferredModel) {
        // Use local model for analysis
        const prompt = this.buildKeyIdeaExtractionPrompt(content, config.min_ideas_required)
        const messages = [{ role: 'user', content: prompt }]

        const response = await localModelService.sendMessage(
          config.local_model_preferred,
          messages
        )

        return this.parseKeyIdeasFromResponse(response, config)
      } else {
        // Fallback to keyword-based extraction
        this.logger.warn('Falling back to keyword extraction', 'extractKeyIdeasWithLocalModel')
        return this.extractKeyIdeasKeywordBased(content, config)
      }
    } catch (error) {
      this.logger.error('Error in local model analysis', 'extractKeyIdeasWithLocalModel', error)

      if (config.fallback_to_keyword_extraction) {
        return this.extractKeyIdeasKeywordBased(content, config)
      }

      throw error
    }
  }

  /**
   * Build prompt for local model keyword extraction (NEW MARKDOWN FORMAT)
   */
  private buildKeyIdeaExtractionPrompt(content: string, minIdeas: number): string {
    return `You are a precise extractor. Do NOT explain. Output exactly ONE fenced Markdown block, nothing else.

Task
- Read the document between DOC and DOC.
- Produce ${minIdeas} key ideas as 1–4 word noun phrases (no verbs, no punctuation).
- Be tolerant: if the doc is short, output as many as you can (≥3).
- Use only these intents: topic, knowledge, connection, action, reference.

Output format (strict)
\`\`\`
!-- FILE_INTEL:BEGIN --
## Key Ideas (1–4 words each)
- text=noun-phrase; score=98; intents=topic,knowledge; context=short hint; entities=Foo,Bar
- text=noun-phrase; score=95; intents=knowledge,action; context=short hint; entities=A,B,C
- text=noun-phrase; score=92; intents=topic; context=short hint; entities=
!-- FILE_INTEL:END --
\`\`\`

Rules
- Exactly one block, no prose outside it.
- Each line starts with "- " and uses key=value pairs separated by "; ".
- text: 1–4 words, letters/numbers/spaces only, Title Case preferred, wrap in quotes.
- score: integer 0–100.
- intents: comma-separated from {topic,knowledge,connection,action,reference}.
- context: ≤10 words, quoted.
- entities: 0+ comma-separated tokens; leave empty if none.
- If you can't find ${minIdeas}, output fewer but keep format.
- Do not include code fences inside the block.

DOC
${content.substring(0, 8000)}${content.length > 8000 ? '\n[Content truncated...]' : ''}
DOC`
  }

  /**
   * Parse markdown FILE_INTEL format into KeyIdea objects
   * NEW LOGIC: Handles format like "- Project Management ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum"
   */
  private parseMarkdownFileIntel(response: string): KeyIdea[] {
    try {
      console.log('[LABELS] 🔄 MARKDOWN: Attempting to parse NEW markdown format...')

      // Look for FILE_INTEL block
      const fileIntelMatch = response.match(/!--\s*FILE_INTEL:BEGIN\s*--[\s\S]*?!--\s*FILE_INTEL:END\s*--/i)
      if (!fileIntelMatch) {
        console.log('[LABELS] 🔄 MARKDOWN: No FILE_INTEL block found')
        return []
      }

      const fileIntelBlock = fileIntelMatch[0]
      console.log('[LABELS] 🔄 MARKDOWN: Found FILE_INTEL block:', fileIntelBlock.substring(0, 200))

      // NEW LOGIC: Extract lines that start with "- " and contain " ; " separators
      // Format: "- Project Management ; score=99; intents=topic,action; context=long-term strategy; entities=Agile,Scrum"
      const ideaLines = fileIntelBlock.match(/^- [^;]+ ;.*$/gm) || []
      console.log('[LABELS] 🔄 MARKDOWN: Found', ideaLines.length, 'idea lines with NEW format')

      const ideas: KeyIdea[] = []

      for (let i = 0; i < ideaLines.length; i++) {
        const line = ideaLines[i]
        console.log('[LABELS] 🔄 MARKDOWN: Parsing NEW format line:', line)

        try {
          // FIXED NEW PARSING LOGIC: Split by " ; " to get text and ALL key=value pairs
          const firstSplit = line.split(' ; ')
          if (firstSplit.length < 2) {
            console.warn('[LABELS] 🔄 MARKDOWN: ⚠️ Line does not match expected format:', line)
            continue
          }

          // First part is "- Text" - extract the text
          const textPart = firstSplit[0].replace(/^-\s*/, '').trim()
          if (!textPart) {
            console.warn('[LABELS] 🔄 MARKDOWN: ⚠️ No text found in line:', line)
            continue
          }

          // Join remaining parts and split by "; " to get individual key=value pairs
          const remainingParts = firstSplit.slice(1).join(' ; ')
          const keyValueParts = remainingParts.split('; ')

          // Parse key=value pairs
          const pairs: Record<string, string> = {}
          for (const part of keyValueParts) {
            const trimmedPart = part.trim()
            const equalIndex = trimmedPart.indexOf('=')
            if (equalIndex > 0) {
              const key = trimmedPart.substring(0, equalIndex).trim()
              const value = trimmedPart.substring(equalIndex + 1).trim()
              pairs[key] = value
            }
          }

          console.log('[LABELS] 🔄 MARKDOWN: Extracted text:', textPart)
          console.log('[LABELS] 🔄 MARKDOWN: Extracted pairs:', pairs)

          const idea: KeyIdea = {
            id: `idea_${Date.now()}_${i}`,
            text: textPart,
            relevance_score: parseInt(pairs.score) || 80,
            intent_types: pairs.intents ? pairs.intents.split(',').map(s => s.trim()) as any[] : ['topic'],
            context: pairs.context || '',
            auto_selected: parseInt(pairs.score) >= 90,
            user_confirmed: false,
            weight: parseInt(pairs.score) || 80
          }

          // Store entities separately for debugging (not part of KeyIdea type)
          if (pairs.entities) {
            console.log('[LABELS] 🔄 MARKDOWN: Entities found for', idea.text, ':', pairs.entities)
          }

          ideas.push(idea)
          console.log('[LABELS] 🔄 MARKDOWN: ✅ Parsed NEW format idea:', idea.text, 'score:', idea.relevance_score)
        } catch (lineError) {
          console.warn('[LABELS] 🔄 MARKDOWN: ⚠️ Failed to parse NEW format line:', line, lineError)
        }
      }

      console.log('[LABELS] 🔄 MARKDOWN: ✅ Successfully parsed', ideas.length, 'ideas from NEW markdown format')

      // DEBUG: Hash consistency check
      console.log('[LABELS] 🔍 HASH-DEBUG: NEW Markdown→JSON conversion completed')
      console.log('[LABELS] 🔍 HASH-DEBUG: Original markdown response length:', response.length)
      console.log('[LABELS] 🔍 HASH-DEBUG: Parsed ideas count:', ideas.length)
      console.log('[LABELS] 🔍 HASH-DEBUG: Ideas preview:', ideas.slice(0, 3).map(i => ({ text: i.text, score: i.relevance_score })))

      return ideas

    } catch (error) {
      console.error('[LABELS] 🔄 MARKDOWN: ❌ Error parsing NEW markdown format:', error)
      return []
    }
  }

  /**
   * Parse key=value pairs from a line like "- text=value; score=98; intents=topic,knowledge"
   */
  private parseKeyValuePairs(line: string): Record<string, string> {
    const pairs: Record<string, string> = {}

    // Remove the leading "- " and split by "; "
    const cleanLine = line.replace(/^-\s*/, '')
    const segments = cleanLine.split(';')

    for (const segment of segments) {
      const trimmed = segment.trim()
      const equalIndex = trimmed.indexOf('=')

      if (equalIndex > 0) {
        const key = trimmed.substring(0, equalIndex).trim()
        const value = trimmed.substring(equalIndex + 1).trim()
        pairs[key] = value
      }
    }

    return pairs
  }

  /**
   * Parse key ideas from local model response (SUPPORTS BOTH JSON AND MARKDOWN)
   */
  private parseKeyIdeasFromResponse(response: string, config: FileIntelligenceConfig): KeyIdea[] {
    try {
      console.log('[LABELS] 🔄 PARSE: Parsing response from local model...')
      console.log('[LABELS] 🔄 PARSE: Response preview:', response.substring(0, 200))

      // Clean the response to handle potential encoding issues
      const cleanedResponse = this.cleanResponseForParsing(response)

      // FIRST: Try to parse new markdown format
      const markdownIdeas = this.parseMarkdownFileIntel(cleanedResponse)
      if (markdownIdeas.length > 0) {
        console.log('[LABELS] 🔄 PARSE: ✅ Successfully parsed markdown format, found', markdownIdeas.length, 'ideas')
        return markdownIdeas
      }

      console.log('[LABELS] 🔄 PARSE: No markdown format found, trying JSON...')

      // FALLBACK: Try to parse JSON response - look for JSON object or array
      let jsonMatch = cleanedResponse.match(/\{[\s\S]*?\}(?=\s*$|\s*```|\s*\n\s*[^}\s])/s)
      if (!jsonMatch) {
        // Try to find JSON array format
        jsonMatch = cleanedResponse.match(/\[[\s\S]*?\](?=\s*$|\s*```|\s*\n\s*[^}\s])/s)
      }
      if (!jsonMatch) {
        // Try to extract from markdown code blocks
        const codeBlockMatch = cleanedResponse.match(/```(?:json)?\s*(\{[\s\S]*?\}|\[[\s\S]*?\])\s*```/s)
        if (codeBlockMatch) {
          jsonMatch = [codeBlockMatch[1]]
        }
      }
      if (!jsonMatch) {
        this.logger.warn('No JSON or markdown found in response, falling back to keyword extraction', 'parseKeyIdeasFromResponse')
        // If AI model returned neither markdown nor JSON, use fallback extraction
        return this.extractKeyIdeasKeywordBased(response, config)
      }

      let parsed: any
      try {
        parsed = JSON.parse(jsonMatch[0])
      } catch (jsonError) {
        this.logger.warn('JSON parsing failed, attempting to fix common issues', 'parseKeyIdeasFromResponse', jsonError)
        // Try to fix common JSON issues with Chinese characters
        const fixedJson = this.fixJsonForChinese(jsonMatch[0])
        parsed = JSON.parse(fixedJson)
      }

      const ideas: KeyIdea[] = []

      if (parsed.key_ideas && Array.isArray(parsed.key_ideas)) {
        parsed.key_ideas.forEach((idea: any, index: number) => {
          // Ensure text is properly encoded and cleaned
          const cleanText = this.cleanTextForStorage(idea.text || '')

          // Filter out long sentences and descriptions
          const wordCount = cleanText.split(/\s+/).length
          const hasLongSentence = cleanText.length > 50 || wordCount > 6
          const hasDescriptiveWords = /\b(the|this|that|these|those|implements|describes|process|following|aspects|features|system|document|content|information)\b/i.test(cleanText)

          if (hasLongSentence || hasDescriptiveWords) {
            this.logger.warn('Filtering out long/descriptive text', 'parseKeyIdeasFromResponse', {
              text: cleanText,
              wordCount,
              length: cleanText.length
            })
            return // Skip this idea
          }

          const keyIdea: KeyIdea = {
            id: `idea_${Date.now()}_${index}`,
            text: cleanText,
            relevance_score: Math.min(100, Math.max(0, idea.relevance_score || 50)),
            intent_types: Array.isArray(idea.intent_types) ? idea.intent_types : ['topic'],
            weight: this.calculateIdeaWeight(idea.relevance_score || 50),
            auto_selected: false, // Will be set later
            user_confirmed: false,
            context: this.cleanTextForStorage(idea.context || ''),
            extracted_from: 'local_model'
          }
          ideas.push(keyIdea)
        })
      }

      // Sort by relevance score and auto-select top N
      ideas.sort((a, b) => b.relevance_score - a.relevance_score)
      ideas.slice(0, config.auto_select_top_n).forEach(idea => {
        idea.auto_selected = true
      })

      this.logger.info('Parsed key ideas from local model', 'parseKeyIdeasFromResponse', {
        totalIdeas: ideas.length,
        autoSelected: ideas.filter(i => i.auto_selected).length
      })

      return ideas
    } catch (error) {
      this.logger.error('Failed to parse local model response', 'parseKeyIdeasFromResponse', error as Error)
      // If parsing fails completely, use fallback extraction
      return this.extractKeyIdeasKeywordBased(response, config)
    }
  }

  /**
   * Get intelligence file path for a document using V02 compliant path joining
   */
  private async getIntelligenceFilePath(vaultPath: string, documentHash: string): Promise<string> {
    try {
      const result = await window.electronAPI.path.join(vaultPath, this.INTELLIGENCE_FOLDER, documentHash, 'intelligence.json')
      return result.success ? result.path! : `${vaultPath}/${this.INTELLIGENCE_FOLDER}/${documentHash}/intelligence.json`
    } catch (error) {
      console.warn('[FileIntelligenceService] Path join failed, using fallback:', error)
      return `${vaultPath}/${this.INTELLIGENCE_FOLDER}/${documentHash}/intelligence.json`
    }
  }

  /**
   * Fallback keyword-based key idea extraction with Chinese character support
   */
  private extractKeyIdeasKeywordBased(content: string, config: FileIntelligenceConfig): KeyIdea[] {
    const ideas: KeyIdea[] = []
    const processedKeywords = new Set<string>()

    // Clean and normalize content for processing
    const cleanContent = this.cleanTextForStorage(content)

    // Extract technical terms and important keywords
    const technicalPatterns = [
      // Programming/Tech terms
      /\b(API|REST|GraphQL|JSON|XML|HTTP|HTTPS|SQL|NoSQL|MongoDB|PostgreSQL|MySQL)\b/gi,
      /\b(React|Vue|Angular|Node\.js|JavaScript|TypeScript|Python|Java|C\+\+)\b/gi,
      /\b(Docker|Kubernetes|AWS|Azure|GCP|CI\/CD|DevOps|Microservices)\b/gi,

      // Business terms
      /\b(Requirements?|Specifications?|Architecture|Implementation|Design)\b/gi,
      /\b(Performance|Security|Testing|Deployment|Integration|Database)\b/gi,
      /\b(User Interface|UI|UX|Frontend|Backend|Full[- ]?Stack)\b/gi,

      // Names (proper nouns) - English
      /\b[A-Z][a-z]+ [A-Z][a-z]+\b/g,

      // Company names - English
      /\b[A-Z][a-z]+ (Inc|Corp|LLC|Ltd|Company|Technologies|Systems|Solutions)\b/g,

      // Email addresses
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,

      // Chinese names (2-4 characters)
      /[\u4e00-\u9fff]{2,4}(?=[\s，。！？；：]|$)/g,

      // Chinese technical terms and concepts
      /[\u4e00-\u9fff]{2,6}(?:系统|平台|技术|方案|架构|设计|开发|管理|服务|应用)/g,

      // Mixed Chinese-English terms
      /[\u4e00-\u9fff]+[A-Za-z]+|[A-Za-z]+[\u4e00-\u9fff]+/g
    ]

    // Extract keywords using patterns
    technicalPatterns.forEach((pattern, patternIndex) => {
      const matches = cleanContent.match(pattern) || []
      matches.forEach((match) => {
        const keyword = this.cleanTextForStorage(match.trim())
        if (keyword.length >= 2 && keyword.length <= 50 && !processedKeywords.has(keyword.toLowerCase())) {
          processedKeywords.add(keyword.toLowerCase())

          // Determine intent type based on pattern
          let intentTypes: ('topic' | 'knowledge' | 'connection' | 'action' | 'reference')[] = ['topic']
          if (patternIndex === 6 || patternIndex === 9) intentTypes = ['connection'] // English/Chinese Names
          if (patternIndex === 7) intentTypes = ['connection'] // Companies
          if (patternIndex === 8) intentTypes = ['connection'] // Emails
          if (patternIndex <= 2 || patternIndex === 10) intentTypes = ['knowledge'] // Technical terms
          if (patternIndex === 11) intentTypes = ['knowledge'] // Mixed terms

          // Higher relevance for Chinese content
          const isChinese = /[\u4e00-\u9fff]/.test(keyword)
          const baseScore = isChinese ? 70 : 60
          const relevanceScore = Math.min(95, baseScore + (keyword.length > 10 ? 20 : 10))

          ideas.push({
            id: `keyword_idea_${ideas.length}`,
            text: keyword,
            relevance_score: relevanceScore,
            intent_types: intentTypes,
            weight: this.calculateIdeaWeight(relevanceScore),
            auto_selected: ideas.length < config.auto_select_top_n,
            user_confirmed: false,
            context: 'pattern_extraction',
            extracted_from: 'keyword_fallback'
          })
        }
      })
    })

    // Extract important noun phrases (2-4 words) - English and Chinese
    const englishNounPhrasePattern = /\b[A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,2}\b/g
    const chineseNounPhrasePattern = /[\u4e00-\u9fff]{2,4}[\s，。]*[\u4e00-\u9fff]{2,4}/g

    const englishPhrases = cleanContent.match(englishNounPhrasePattern) || []
    const chinesePhrases = cleanContent.match(chineseNounPhrasePattern) || []
    const allPhrases = [...englishPhrases, ...chinesePhrases]

    allPhrases.forEach(phrase => {
      const trimmed = this.cleanTextForStorage(phrase.trim())
      if (trimmed.length >= 4 && trimmed.length <= 40 && !processedKeywords.has(trimmed.toLowerCase())) {
        processedKeywords.add(trimmed.toLowerCase())

        const isChinese = /[\u4e00-\u9fff]/.test(trimmed)
        const relevanceScore = isChinese ? 75 : 70

        ideas.push({
          id: `keyword_idea_${ideas.length}`,
          text: trimmed,
          relevance_score: relevanceScore,
          intent_types: ['topic'],
          weight: this.calculateIdeaWeight(relevanceScore),
          auto_selected: ideas.length < config.auto_select_top_n,
          user_confirmed: false,
          context: 'noun_phrase_extraction',
          extracted_from: 'keyword_fallback'
        })
      }
    })

    // If we still don't have enough keywords, extract important single words
    if (ideas.length < config.min_ideas_required) {
      const importantWords = [
        // English terms
        'requirement', 'specification', 'design', 'architecture', 'implementation',
        'feature', 'component', 'system', 'interface', 'security', 'performance',
        'testing', 'deployment', 'integration', 'database', 'authentication',
        'authorization', 'optimization', 'scalability', 'monitoring', 'analytics',
        // Chinese terms
        '需求', '规格', '设计', '架构', '实现', '功能', '组件', '系统', '接口', '安全',
        '性能', '测试', '部署', '集成', '数据库', '认证', '授权', '优化', '监控', '分析'
      ]

      importantWords.forEach(word => {
        const lowerWord = word.toLowerCase()
        if ((cleanContent.toLowerCase().includes(lowerWord) || cleanContent.includes(word)) &&
            !processedKeywords.has(lowerWord)) {
          processedKeywords.add(lowerWord)

          const isChinese = /[\u4e00-\u9fff]/.test(word)
          const displayWord = isChinese ? word : word.charAt(0).toUpperCase() + word.slice(1)

          ideas.push({
            id: `keyword_idea_${ideas.length}`,
            text: displayWord,
            relevance_score: 50,
            intent_types: ['topic'],
            weight: this.calculateIdeaWeight(50),
            auto_selected: ideas.length < config.auto_select_top_n,
            user_confirmed: false,
            context: 'important_word_extraction',
            extracted_from: 'keyword_fallback'
          })
        }
      })
    }

    return ideas.slice(0, Math.max(config.min_ideas_required, 15))
  }

  /**
   * Calculate idea weight based on relevance score
   */
  private calculateIdeaWeight(relevanceScore: number): number {
    return Math.min(1.0, relevanceScore / 100)
  }

  /**
   * Extract file metadata
   */
  private async extractFileMetadata(filePath: string, content: string): Promise<any> {
    const stats = {
      path: filePath,
      type: filePath.split('.').pop()?.toLowerCase() || 'unknown',
      size_bytes: content.length,
      hash: this.generateDocumentHash(filePath),
      last_modified: new Date().toISOString()
    }
    return stats
  }

  /**
   * Clean response text for JSON parsing, handling Chinese characters
   */
  private cleanResponseForParsing(response: string): string {
    try {
      // Remove any BOM (Byte Order Mark) that might interfere with parsing
      let cleaned = response.replace(/^\uFEFF/, '')

      // Normalize Unicode characters (especially important for Chinese text)
      cleaned = cleaned.normalize('NFC')

      // Remove any control characters that might break JSON parsing
      cleaned = cleaned.replace(/[\x00-\x1F\x7F]/g, '')

      // Fix common encoding issues with quotes in Chinese text
      cleaned = cleaned.replace(/[""]/g, '"')
      cleaned = cleaned.replace(/['']/g, "'")

      return cleaned
    } catch (error) {
      this.logger.warn('Error cleaning response for parsing', 'cleanResponseForParsing', error)
      return response
    }
  }

  /**
   * Fix JSON string to handle Chinese characters properly
   */
  private fixJsonForChinese(jsonString: string): string {
    try {
      // Handle escaped Unicode sequences that might be malformed
      let fixed = jsonString

      // Fix common issues with Chinese characters in JSON
      // Replace any malformed escape sequences
      fixed = fixed.replace(/\\u([0-9a-fA-F]{4})/g, (match, code) => {
        try {
          return String.fromCharCode(parseInt(code, 16))
        } catch {
          return match // Keep original if conversion fails
        }
      })

      // Ensure proper quote escaping for Chinese text
      fixed = fixed.replace(/([^\\])"([^"]*[\u4e00-\u9fff][^"]*)"([^:])/g, '$1"$2"$3')

      return fixed
    } catch (error) {
      this.logger.warn('Error fixing JSON for Chinese characters', 'fixJsonForChinese', error)
      return jsonString
    }
  }

  /**
   * Clean text for safe storage, preserving Chinese characters
   */
  private cleanTextForStorage(text: string): string {
    try {
      if (!text || typeof text !== 'string') {
        return ''
      }

      // Normalize Unicode (important for Chinese characters)
      let cleaned = text.normalize('NFC')

      // Remove control characters but preserve Chinese characters
      cleaned = cleaned.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')

      // Trim whitespace
      cleaned = cleaned.trim()

      // Limit length to prevent storage issues
      if (cleaned.length > 500) {
        cleaned = cleaned.substring(0, 497) + '...'
      }

      return cleaned
    } catch (error) {
      this.logger.warn('Error cleaning text for storage', 'cleanTextForStorage', error)
      return text || ''
    }
  }

  /**
   * Serialize data for storage with proper Chinese character handling
   */
  private serializeDataForStorage(data: any): string {
    try {
      // Custom replacer function to handle Chinese characters properly
      const replacer = (_key: string, value: any) => {
        if (typeof value === 'string') {
          // Clean and normalize text values
          return this.cleanTextForStorage(value)
        }
        return value
      }

      // Use JSON.stringify with proper spacing and the replacer
      const jsonString = JSON.stringify(data, replacer, 2)

      // Ensure the JSON is valid UTF-8
      return jsonString.normalize('NFC')
    } catch (error) {
      this.logger.error('Error serializing data for storage', 'serializeDataForStorage', error as Error)
      // Fallback to basic JSON.stringify
      return JSON.stringify(data, null, 2)
    }
  }

  /**
   * Analyze document structure
   */
  private analyzeDocumentStructure(content: string): any {
    const lines = content.split('\n')
    const words = content.split(/\s+/).filter(w => w.length > 0)

    return {
      word_count: words.length,
      line_count: lines.length,
      character_count: content.length,
      estimated_reading_time: Math.ceil(words.length / 200) // 200 words per minute
    }
  }

  /**
   * Extract weighted entities with priority classification
   */
  private async extractWeightedEntities(content: string, keyIdeas: KeyIdea[]): Promise<WeightedEntity[]> {
    const entities: WeightedEntity[] = []

    // Extract people names (high priority)
    const namePattern = /\b[A-Z][a-z]+ [A-Z][a-z]+\b/g
    const names = content.match(namePattern) || []
    names.forEach(name => {
      entities.push({
        text: name,
        type: 'person',
        confidence: 0.8,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Name pattern detection',
        priority_level: 'high'
      })
    })

    // Extract email addresses (high priority)
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g
    const emails = content.match(emailPattern) || []
    emails.forEach(email => {
      entities.push({
        text: email,
        type: 'email',
        confidence: 0.95,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Email pattern detection',
        priority_level: 'high'
      })
    })

    // Extract company names (high priority) - simple heuristic
    const companyPattern = /\b[A-Z][a-z]+ (Inc|Corp|LLC|Ltd|Company|Technologies|Systems|Solutions)\b/g
    const companies = content.match(companyPattern) || []
    companies.forEach(company => {
      entities.push({
        text: company,
        type: 'company',
        confidence: 0.7,
        weight: PRIORITY_WEIGHTS.high,
        intent_types: ['connection'],
        context: 'Company pattern detection',
        priority_level: 'high'
      })
    })

    return entities
  }

  /**
   * Extract human connections from entities
   */
  private async extractHumanConnections(content: string, entities: WeightedEntity[]): Promise<HumanConnection[]> {
    const connections: HumanConnection[] = []

    const people = entities.filter(e => e.type === 'person')
    const emails = entities.filter(e => e.type === 'email')
    const companies = entities.filter(e => e.type === 'company')

    people.forEach(person => {
      const connection: HumanConnection = {
        name: person.text,
        connection_strength: person.confidence,
        collaboration_context: person.context,
        document_mentions: 1,
        priority_weight: 1.0
      }

      // Try to find associated email
      const associatedEmail = emails.find(e =>
        content.indexOf(person.text) !== -1 && content.indexOf(e.text) !== -1
      )
      if (associatedEmail) {
        connection.email = associatedEmail.text
      }

      // Try to find associated company
      const associatedCompany = companies.find(c =>
        content.indexOf(person.text) !== -1 && content.indexOf(c.text) !== -1
      )
      if (associatedCompany) {
        connection.company = associatedCompany.text
      }

      connections.push(connection)
    })

    return connections
  }

  /**
   * Ensure intelligence directory exists using V02 compliant path joining
   */
  private async ensureIntelligenceDirectory(vaultPath: string, documentHash: string): Promise<void> {
    try {
      const pathResult = await window.electronAPI.path.join(vaultPath, this.INTELLIGENCE_FOLDER, documentHash)
      const intelligenceDir = pathResult.success ? pathResult.path! : `${vaultPath}/${this.INTELLIGENCE_FOLDER}/${documentHash}`

      if (window.electronAPI?.vault?.createDirectory) {
        const result = await window.electronAPI.vault.createDirectory(intelligenceDir)
        if (!result.success) {
          throw new ServiceError(
            ServiceErrorCode.STORAGE_ERROR,
            `Failed to create intelligence directory: ${result.error}`,
            { serviceName: this.serviceName, operation: 'ensureIntelligenceDirectory' }
          )
        }
      }
    } catch (error) {
      console.warn('[FileIntelligenceService] Path join failed in ensureIntelligenceDirectory:', error)
      throw new ServiceError(
        ServiceErrorCode.STORAGE_ERROR,
        `Failed to ensure intelligence directory: ${error}`,
        { serviceName: this.serviceName, operation: 'ensureIntelligenceDirectory' }
      )
    }
  }

  /**
   * Categorize entities by priority level
   */
  private categorizeEntitiesByPriority(entities: WeightedEntity[]): any {
    return {
      high_priority: entities.filter(e => e.priority_level === 'high'),
      medium_priority: entities.filter(e => e.priority_level === 'medium'),
      low_priority: entities.filter(e => e.priority_level === 'low')
    }
  }

  /**
   * Calculate overall confidence score
   */
  private calculateOverallConfidence(keyIdeas: KeyIdea[], entities: WeightedEntity[]): number {
    if (keyIdeas.length === 0) return 0

    const avgIdeaScore = keyIdeas.reduce((sum, idea) => sum + idea.relevance_score, 0) / keyIdeas.length
    const avgEntityConfidence = entities.length > 0
      ? entities.reduce((sum, entity) => sum + entity.confidence, 0) / entities.length
      : 0.5

    return Math.min(1.0, (avgIdeaScore / 100 + avgEntityConfidence) / 2)
  }

  /**
   * Extract file references from content
   */
  private extractFileReferences(content: string): string[] {
    const filePattern = /[\w\-_]+\.(pdf|doc|docx|txt|md|xlsx|ppt|pptx)/gi
    const matches = content.match(filePattern) || []
    return [...new Set(matches)] // Remove duplicates
  }

  /**
   * Store file intelligence data in context vault
   */
  private async storeFileIntelligence(filePath: string, intelligence: FileIntelligence): Promise<void> {
    try {
      // Extract context path from file path
      const contextPath = this.extractContextPath(filePath)
      if (!contextPath) {
        this.logger.warn('No context path found for file, skipping storage', 'storeFileIntelligence', { filePath })
        return
      }

      // Use direct kernel intelligence client for storage
      await intelligenceClient.write(filePath, contextPath, { json: intelligence })

      this.logger.info('File intelligence stored successfully', 'storeFileIntelligence', {
        filePath,
        contextPath,
      })
    } catch (error) {
      this.logger.error('Failed to store file intelligence', 'storeFileIntelligence', error as Error)
      throw error
    }
  }

  /**
   * Extract context path from file path using V02 compliant approach
   * ✅ V02 COMPLIANT: Uses simple path parsing logic until PathResolver.inferVaultPath is exposed via IPC
   */
  private extractContextPath(filePath: string): string | null {
    console.log('[LABELS] 🗂️ V02 extractContextPath called with:', filePath)

    if (!filePath || typeof filePath !== 'string') {
      console.log('[LABELS] 🗂️ Invalid filePath, returning null')
      return null
    }

    try {
      // Simple strategy: look for common subdirectories and extract parent
      const normalizedPath = filePath.replace(/\\/g, '/')
      const subDirs = ['documents', 'images', 'artifacts']

      for (const subDir of subDirs) {
        const token = `/${subDir}/`
        const idx = normalizedPath.lastIndexOf(token)
        if (idx !== -1) {
          const contextPath = normalizedPath.substring(0, idx)
          // Restore original separator style
          const result = filePath.includes('\\') ? contextPath.replace(/\//g, '\\') : contextPath
          console.log('[LABELS] 🗂️ ✅ Context path extracted:', result)
          return result
        }
      }

      console.warn('[LABELS] 🗂️ No context path found for file:', filePath)
      return null
    } catch (error) {
      console.error('[LABELS] 🗂️ Error in extractContextPath:', error)
      return null
    }
  }

  /**
   * Update processing progress
   */
  private updateProgress(
    filePath: string,
    stage: ProcessingProgress['stage'],
    percentage: number,
    operation: string
  ): void {
    const progress: ProcessingProgress = {
      file_path: filePath,
      stage,
      progress_percentage: percentage,
      current_operation: operation
    }

    this.processingQueue.set(filePath, progress)

    // Emit progress event for UI updates
    // TODO: Fix events API when available
    // if (typeof window !== 'undefined' && window.electronAPI?.events?.emit) {
    //   window.electronAPI.events.emit('file-intelligence-progress', progress)
    // }
  }

  /**
   * Get current processing progress for a file
   */
  getProcessingProgress(filePath: string): ProcessingProgress | null {
    return this.processingQueue.get(filePath) || null
  }

  /**
   * Clear processing progress for a file
   */
  clearProcessingProgress(filePath: string): void {
    this.processingQueue.delete(filePath)
  }

  // ============================================================================
  // LEGACY INTELLIGENCE DATA METHODS (Backward Compatibility)
  // ============================================================================

  /**
   * Save file intelligence data
   */
  async saveFileIntelligence(vaultPath: string, data: FileIntelligenceData): Promise<boolean> {
    const result = await this.executeOperation(
      'saveFileIntelligence',
      async () => {
        await this.ensureIntelligenceDirectory(vaultPath, data.document_hash)
        
        const filePath = await this.getIntelligenceFilePath(vaultPath, data.document_hash)
        
        if (window.electronAPI?.vault?.writeFile) {
          // Ensure proper JSON serialization with Chinese character support
          const jsonData = this.serializeDataForStorage(data)
          const writeResult = await window.electronAPI.vault.writeFile(filePath, jsonData)
          if (!writeResult.success) {
            throw new ServiceError(
              ServiceErrorCode.STORAGE_ERROR,
              `Failed to save intelligence data: ${writeResult.error}`,
              { serviceName: this.serviceName, operation: 'saveFileIntelligence' }
            )
          }
        }
        
        return true
      }
    )

    if (!result.success) {
      this.logger.error('Failed to save file intelligence', 'saveFileIntelligence', result.error)
      // toastService.error('Save Failed', 'Could not save intelligence data')
      return false
    }

    return result.data!
  }

  /**
   * Load file intelligence data
   */
  async loadFileIntelligence(vaultPath: string, filePath: string): Promise<FileIntelligenceData | null> {
    const result = await this.executeOperation(
      'loadFileIntelligence',
      async () => {
        const documentHash = this.generateDocumentHash(filePath)
        const intelligenceFilePath = await this.getIntelligenceFilePath(vaultPath, documentHash)
        
        if (window.electronAPI?.vault?.readFile) {
          const readResult = await window.electronAPI.vault.readFile(intelligenceFilePath)
          if (!readResult.success) {
            // File doesn't exist yet, return null
            return null
          }
          
          try {
            const data = JSON.parse(readResult.content!) as FileIntelligenceData
            return data
          } catch (error) {
            this.logger.warn('Invalid intelligence data format', 'loadFileIntelligence', error)
            return null
          }
        }
        
        return null
      }
    )

    if (!result.success) {
      this.logger.error('Failed to load file intelligence', 'loadFileIntelligence', result.error)
      return null
    }

    return result.data!
  }

  /**
   * Create initial file intelligence data structure
   */
  createInitialIntelligenceData(filePath: string, fileName: string, vaultName: string, contextId?: string): FileIntelligenceData {
    const documentHash = this.generateDocumentHash(filePath)
    
    return {
      document_hash: documentHash,
      file_path: filePath,
      file_name: fileName,
      file_type: fileName.split('.').pop()?.toLowerCase() || 'unknown',
      vault_name: vaultName,
      context_id: contextId,
      last_updated: new Date().toISOString(),
      entity_selections: [],
      smart_annotations: [],
      user_notes: [],
      ai_analysis: null,
      interaction_history: [],
      annotation_navigation: {
        current_note_index: 0,
        total_notes: 0,
        note_order: [],
        last_viewed_note: null,
        last_navigation_timestamp: new Date().toISOString(),
        created_timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Generate mock entities for demonstration (will be replaced with AI analysis)
   */
  generateMockEntities(fileName: string): EntitySelection[] {
    const fileExtension = fileName.split('.').pop()?.toLowerCase() || ''
    
    const mockEntities: Partial<EntitySelection>[] = []
    
    // Generate entities based on file type
    if (fileExtension === 'pdf') {
      mockEntities.push(
        { entity_text: 'UI Design', entity_type: 'content_category', confidence: 0.95 },
        { entity_text: 'Specifications', entity_type: 'technical_concept', confidence: 0.88 },
        { entity_text: 'Requirements', entity_type: 'requirement', confidence: 0.82 },
        { entity_text: 'Components', entity_type: 'feature', confidence: 0.75 },
        { entity_text: 'Design System', entity_type: 'methodology', confidence: 0.70 },
        { entity_text: 'User Interface', entity_type: 'technical_concept', confidence: 0.65 }
      )
    } else if (fileExtension === 'md') {
      mockEntities.push(
        { entity_text: 'Documentation', entity_type: 'content_category', confidence: 0.92 },
        { entity_text: 'API Reference', entity_type: 'technical_concept', confidence: 0.85 },
        { entity_text: 'Code Examples', entity_type: 'feature', confidence: 0.78 },
        { entity_text: 'Installation', entity_type: 'action_item', confidence: 0.72 },
        { entity_text: 'Configuration', entity_type: 'requirement', confidence: 0.68 }
      )
    } else {
      mockEntities.push(
        { entity_text: 'File Content', entity_type: 'content_category', confidence: 0.80 },
        { entity_text: 'Data Structure', entity_type: 'technical_concept', confidence: 0.75 },
        { entity_text: 'Information', entity_type: 'other', confidence: 0.70 }
      )
    }

    // Convert to full EntitySelection objects
    return mockEntities.map((entity, index) => ({
      entity_id: `entity_${Date.now()}_${index}`,
      entity_text: entity.entity_text!,
      entity_type: entity.entity_type!,
      confidence: entity.confidence!,
      is_selected: index < 3, // Pre-select top 3
      selection_timestamp: new Date().toISOString(),
      color_category: index === 0 ? 'primary' : index === 1 ? 'secondary' : index === 2 ? 'tertiary' : 'default',
      rank: index + 1,
      context_snippet: `Context for ${entity.entity_text}`
    }))
  }

  /**
   * Update entity selections
   */
  async updateEntitySelections(vaultPath: string, filePath: string, selections: EntitySelection[]): Promise<boolean> {
    const data = await this.loadFileIntelligence(vaultPath, filePath)
    if (!data) return false

    data.entity_selections = selections
    data.last_updated = new Date().toISOString()
    
    // Add interaction record
    const interaction: FileInteraction = {
      interaction_id: `int_${Date.now()}`,
      action_type: 'entity_select',
      timestamp: new Date().toISOString(),
      data: { selections: selections.map(s => ({ entity_id: s.entity_id, is_selected: s.is_selected })) },
      session_id: `session_${Date.now()}`
    }
    data.interaction_history.push(interaction)

    return await this.saveFileIntelligence(vaultPath, data)
  }

  /**
   * Add smart annotation
   */
  async addSmartAnnotation(vaultPath: string, filePath: string, annotation: SmartAnnotation): Promise<boolean> {
    const data = await this.loadFileIntelligence(vaultPath, filePath)
    if (!data) return false

    data.smart_annotations.push(annotation)
    data.last_updated = new Date().toISOString()
    
    // Add interaction record
    const interaction: FileInteraction = {
      interaction_id: `int_${Date.now()}`,
      action_type: 'annotation_create',
      timestamp: new Date().toISOString(),
      data: { annotation_id: annotation.annotation_id },
      session_id: `session_${Date.now()}`
    }
    data.interaction_history.push(interaction)

    return await this.saveFileIntelligence(vaultPath, data)
  }

  /**
   * Update smart annotation
   */
  async updateSmartAnnotation(vaultPath: string, filePath: string, annotationId: string, content: string): Promise<boolean> {
    const data = await this.loadFileIntelligence(vaultPath, filePath)
    if (!data) return false

    const annotation = data.smart_annotations.find(a => a.annotation_id === annotationId)
    if (!annotation) return false

    annotation.content = content
    annotation.last_edited = new Date().toISOString()
    data.last_updated = new Date().toISOString()
    
    // Add interaction record
    const interaction: FileInteraction = {
      interaction_id: `int_${Date.now()}`,
      action_type: 'annotation_edit',
      timestamp: new Date().toISOString(),
      data: { annotation_id: annotationId, new_content: content },
      session_id: `session_${Date.now()}`
    }
    data.interaction_history.push(interaction)

    return await this.saveFileIntelligence(vaultPath, data)
  }

  // 🚨 PATH VIOLATION REMOVED: createFileHash() function was deprecated
  // REASON: Duplicated hash generation logic from intelligenceStorageService.ts
  // RESOLUTION: Use intelligenceStorageService.generateFileHash() instead for consistency
}

export const fileIntelligenceService = new FileIntelligenceService()
