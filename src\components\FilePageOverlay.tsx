import React, { useState, useEffect, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICONS } from './Icons/index';
import { pdfViewerService, PDFViewerState } from '../services/PDFViewerService';
import { fileViewerService, FileViewerState } from '../services/FileViewerService';
import { SmartLabelingInterface } from './SmartLabelingInterface';
import { 
  KeyIdea, 
  ProcessingResult, 
  FileIntelligence, 
  HumanConnection,
  SmartAnnotationNote
} from '../types/fileIntelligenceTypes';
// Removed: fileAnalysisService - SmartLabelingInterface handles all analysis
// Removed: useAppStore - SmartLabelingInterface handles model selection
import * as pdfjsLib from 'pdfjs-dist';
import { extractContextPath as extractContextPathUtil } from '../utils/vaultPath';
import { askAINavigationService } from '../services/askAINavigationService';
import { intelligence as intelligenceClient } from '../api/UnifiedAPIClient'
import { annotationStorageService } from '../services/annotationStorageService'

// Set up PDF.js worker - use local copy in public directory
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';

// Helper function to properly decode base64 to UTF-8
const decodeBase64ToUTF8 = (base64String: string): string => {
  try {
    const binaryString = atob(base64String);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    const decoder = new TextDecoder('utf-8');
    return decoder.decode(bytes);
  } catch (error) {
    console.error('Error decoding base64 to UTF-8:', error);
    return base64String; // Return original if decoding fails
  }
};

interface FilePageOverlayProps {
  onClose: () => void;
}

interface FileTypeInfo {
  type: 'pdf' | 'markdown' | 'mermaid' | 'text' | 'image' | 'code' | 'unsupported';
  extension: string;
  mimeType?: string;
  canExtractText: boolean;
  canAnnotate: boolean;
  requiresProcessing: boolean;
  extractionMethod: string;
  displayName: string;
}

export const FilePageOverlay: React.FC<FilePageOverlayProps> = ({ onClose }) => {
  console.log('[ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered');

  // Get selected model from store
  // Removed: settings - SmartLabelingInterface handles model selection
  
  // Existing state
  const [state, setState] = useState<FileViewerState>(fileViewerService.getState());
  const [pdfState, setPdfState] = useState<PDFViewerState>(pdfViewerService.getState());
  const [pdfDocument, setPdfDocument] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [fileTypeInfo, setFileTypeInfo] = useState<FileTypeInfo | null>(null);
  const [fileContent, setFileContent] = useState<string>('');
  const [editedContent, setEditedContent] = useState<string>('');
  const [isEditMode] = useState<boolean>(false); // Removed setIsEditMode - editing not implemented
  // Removed: selectedLabels - derived from fileIntelligence
  // Removed: _processingResult - SmartLabelingInterface handles processing
  const [isPdfReady, setIsPdfReady] = useState<boolean>(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const promptTextareaRef = useRef<HTMLTextAreaElement>(null);

  // Enhanced intelligence state
  const [fileIntelligence, setFileIntelligence] = useState<FileIntelligence | null>(null);
  const [humanConnections, setHumanConnections] = useState<HumanConnection[]>([]);
  const [documentSummary, setDocumentSummary] = useState<string>('');
  const [isLoadingIntelligence, setIsLoadingIntelligence] = useState<boolean>(false);
  const [intelligenceError, setIntelligenceError] = useState<string | null>(null);
  const [customPromptText, setCustomPromptText] = useState<string>('');

  // V02 UNIFIED: Smart annotation state
  const [annotations, setAnnotations] = useState<SmartAnnotationNote[]>([]);
  const [currentAnnotationIndex, setCurrentAnnotationIndex] = useState<number>(0);
  const [isProcessingAnnotation, setIsProcessingAnnotation] = useState<boolean>(false);
  const [isEditingAnnotation, setIsEditingAnnotation] = useState<boolean>(false);
  const [editedAnnotationContent, setEditedAnnotationContent] = useState<string>('');
  const [showAllPrompts, setShowAllPrompts] = useState<boolean>(false);
  const [availableHeight, setAvailableHeight] = useState<number>(window.innerHeight);

  // Example prompts for annotation
  const examplePrompts = [
    "Summarize the second paragraph",
    "Extract all color codes and create a palette reference",
    "List all component specifications with their properties",
    "Identify potential implementation challenges and solutions",
    "Compare this with existing design systems and best practices",
    "Generate test cases for accessibility compliance"
  ];

  // Calculate responsive heights for text areas
  const calculateTextAreaHeight = (baseHeight: number, minHeight: number, maxHeight: number) => {
    const calculatedHeight = Math.min(maxHeight, Math.max(minHeight, availableHeight * baseHeight));
    return `${calculatedHeight}px`;
  };

  // Calculate dynamic height for annotation content to ensure buttons are visible
  const calculateAnnotationHeight = () => {
    const totalHeight = availableHeight;
    const headerHeight = 120; // Approximate height for headers and other elements
    const buttonHeight = 80; // Height needed for action buttons
    const availableSpace = totalHeight - headerHeight - buttonHeight;
    
    // Ensure we have enough space for buttons, adjust content height if needed
    if (availableSpace < 100) {
      return Math.max(60, availableSpace - 40); // Leave some padding
    }
    
    return Math.min(120, Math.max(80, availableSpace * 0.6));
  };

  // Update available height on window resize
  useEffect(() => {
    const handleResize = () => {
      setAvailableHeight(window.innerHeight);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Helper function to check if file supports editing
  // Removed: supportsEdit - editing functionality not implemented

  // Extract context path from file path (shared utility)
  const extractContextPath = (filePath: string): string => {
    return extractContextPathUtil(filePath);
  };

  // REMOVED: loadFileIntelligence - SmartLabelingInterface is now the single source of truth for intelligence loading

  // Generate document summary from intelligence data
  const generateDocumentSummary = (intelligence: FileIntelligence): void => {
    const selectedIdeas = intelligence.key_ideas.filter(idea => idea.auto_selected || idea.user_confirmed);
    const topIdeas = selectedIdeas.slice(0, 3);
    
    let summary = "Document Summary:\n\n";
    
    if (topIdeas.length > 0) {
      summary += "Key insights from this document:\n";
      topIdeas.forEach((idea) => {
        summary += `• ${idea.text}\n`;
      });
    }
    
    if (intelligence.human_connections.length > 0) {
      summary += `\nHuman Connections (${intelligence.human_connections.length}):\n`;
      intelligence.human_connections.slice(0, 2).forEach(connection => {
        summary += `• ${connection.name}${connection.title ? ` (${connection.title})` : ''}${connection.email ? ` - ${connection.email}` : ''}\n`;
      });
    }
    
    summary += `\nProcessing confidence: ${Math.round(intelligence.processing_confidence * 100)}%`;
    if (intelligence.analysis_metadata.model_used) {
      summary += `\nAnalyzed with: ${intelligence.analysis_metadata.model_used}`;
    }
    
    setDocumentSummary(summary);
  };

  // Compute sorted ideas by relevance desc
  const getSortedIdeas = (intelligence?: FileIntelligence): KeyIdea[] => {
    const ideas = intelligence?.key_ideas || [];
    return [...ideas].sort((a, b) => b.relevance_score - a.relevance_score);
  };

  // Add a manual label and persist to JSON with heavy weight
  const handleAddManualLabel = async (): Promise<void> => {
    if (!state.filePath || !fileIntelligence) return;
    const label = window.prompt('Enter a label (1–4 words, letters/numbers/spaces only)');
    if (!label) return;

    const trimmed = label.trim();
    // Basic validation: 1–4 words, alnum+space
    const wordCount = trimmed.split(/\s+/).filter(Boolean).length;
    if (wordCount === 0 || wordCount > 4 || /[^\w\s]/.test(trimmed)) {
      alert('Invalid label. Use 1–4 words, letters/numbers/spaces only.');
      return;
    }

    const manualIdea: KeyIdea = {
      id: `manual_idea_${Date.now()}`,
      text: trimmed,
      relevance_score: 99,
      intent_types: ['topic'],
      weight: 1.0,
      auto_selected: true,
      user_confirmed: true,
      context: 'manual',
      extracted_from: 'user_manual'
    };

    // Update local state
    const updated: FileIntelligence = {
      ...fileIntelligence,
      key_ideas: [manualIdea, ...fileIntelligence.key_ideas],
      updated_at: new Date().toISOString()
    };
    setFileIntelligence(updated);
    generateDocumentSummary(updated);

    // V02 COMPLIANT: Let SmartLabelingInterface handle persistence
    // Manual labels will be persisted when SmartLabelingInterface processes the updated data
    console.log('✅ [INTELLIGENCE] Manual label added to UI (persistence handled by SmartLabelingInterface):', manualIdea.text);
  };

  // REMOVED: processFileIntelligence - SmartLabelingInterface handles all intelligence processing

  // Handle intelligence data from SmartLabelingInterface (single source of truth)
  const handleLabelsChanged = (allIdeas: KeyIdea[]): void => {
    console.log('[LABELS] 🎯 OVERLAY: handleLabelsChanged called with', allIdeas.length, 'ideas')
    console.log('[LABELS] 🎯 OVERLAY: Received ideas:', allIdeas.map(idea => ({
      id: idea.id,
      text: idea.text,
      relevance: idea.relevance_score,
      autoSelected: idea.auto_selected,
      userConfirmed: idea.user_confirmed
    })))
    console.log('[LABELS] 🎯 OVERLAY: Current fileIntelligence state:', fileIntelligence ? 'exists' : 'null');

    // Always create/update fileIntelligence from SmartLabelingInterface data
    // Even if allIdeas is empty, we create the intelligence object to show the UI is ready
    const intelligence: FileIntelligence = {
      file_path: state.filePath || '',
      key_ideas: allIdeas,
      weighted_entities: [], // SmartLabelingInterface doesn't provide these yet
      human_connections: [], // SmartLabelingInterface doesn't provide these yet
      processing_confidence: allIdeas.length > 0 ? 0.85 : 0,
      analysis_metadata: {
        processing_time_ms: 0,
        model_used: allIdeas.length > 0 ? 'smart_labeling_interface' : 'none',
        timestamp: new Date().toISOString()
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('[LABELS] 🎯 OVERLAY: Setting fileIntelligence with', intelligence.key_ideas.length, 'ideas')
    setFileIntelligence(intelligence);
    setHumanConnections([]); // Reset since SmartLabelingInterface doesn't provide these

    // Only generate summary if we have ideas
    if (allIdeas.length > 0) {
      generateDocumentSummary(intelligence);
    } else {
      setDocumentSummary('');
    }

    console.log('[LABELS] 🎯 OVERLAY: ✅ Updated fileIntelligence from SmartLabelingInterface:', {
      totalIdeas: intelligence.key_ideas.length,
      selectedIdeas: allIdeas.filter(idea => idea.auto_selected || idea.user_confirmed).length,
      fileIntelligenceSet: true,
      hasIdeas: allIdeas.length > 0
    });

    // Merge existing annotations and persist labels & notes
    (async () => {
      try {
        if (!state.filePath) return;
        const contextPath = extractContextPath(state.filePath);

        // Load existing enriched intelligence (to keep previous notes)
        // Use new .intelligence structure for reading
        const apiResult: any = await window.electronAPI.invoke('intelligence:listSessions', state.filePath, contextPath)
        const existing = apiResult && apiResult.success !== false ? (apiResult.data || null) : null
        const existingAnnotations: SmartAnnotationNote[] = (existing as any)?.smart_annotations || [];

        let merged: FileIntelligence = {
          ...intelligence,
          smart_annotations: existingAnnotations
        };

        // Ensure an initial summary note exists when we have ideas
        if (allIdeas.length > 0) {
          const hasSummary = (merged.smart_annotations || []).some(n => n.type === 'summary');
          if (!hasSummary) {
            const summaryNote: SmartAnnotationNote = {
              id: `note_summary_${Date.now()}`,
              type: 'summary',
              title: 'Auto Summary',
              content: documentSummary || '',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              labels_snapshot: allIdeas
            };
            merged = { ...merged, smart_annotations: [summaryNote, ...(merged.smart_annotations || [])] };
          }
        }

        // V02 COMPLIANT: Let SmartLabelingInterface handle all persistence
        // FilePageOverlay is now read-only for intelligence data
        setFileIntelligence(merged);
        console.log('✅ [INTELLIGENCE] Updated UI state (persistence handled by SmartLabelingInterface)');
      } catch (e) {
        console.warn('⚠️ [INTELLIGENCE] Failed to merge/persist notes:', e);
      }
    })();
  };

  // Handle processing completion from SmartLabelingInterface
  const handleProcessingComplete = async (result: ProcessingResult): Promise<void> => {
    console.log('🎯 [OVERLAY] Processing complete signal received:', result);
    // SmartLabelingInterface will call handleLabelsChanged with the new data
    // No need to reload here since SmartLabelingInterface is the single source of truth
  };

  // File type detection function
  const detectFileType = (fileName: string): FileTypeInfo => {
    const extension = fileName.split('.').pop()?.toLowerCase() || '';

    // PDF files
    if (extension === 'pdf') {
      return {
        type: 'pdf',
        extension,
        mimeType: 'application/pdf',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: true,
        extractionMethod: 'pdf-parse',
        displayName: 'PDF Document'
      };
    }

    // Markdown files
    if (['md', 'markdown'].includes(extension)) {
      return {
        type: 'markdown',
        extension,
        mimeType: 'text/markdown',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'Markdown Document'
      };
    }

    // Image files
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'bmp', 'ico'].includes(extension)) {
      return {
        type: 'image',
        extension,
        mimeType: `image/${extension === 'jpg' ? 'jpeg' : extension}`,
        canExtractText: false,
        canAnnotate: false,
        requiresProcessing: false,
        extractionMethod: 'none',
        displayName: 'Image File'
      };
    }

    // Text files
    if (['txt', 'log', 'csv', 'xml', 'json', 'yaml', 'yml'].includes(extension)) {
      return {
        type: 'text',
        extension,
        mimeType: 'text/plain',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'Text File'
      };
    }

    // Code files
    if (['js', 'ts', 'tsx', 'jsx', 'html', 'css', 'scss', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt'].includes(extension)) {
      return {
        type: 'code',
        extension,
        mimeType: 'text/plain',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'Code File'
      };
    }

    // Unsupported
    return {
      type: 'unsupported',
      extension,
      mimeType: 'application/octet-stream',
      canExtractText: false,
      canAnnotate: false,
      requiresProcessing: false,
      extractionMethod: 'none',
      displayName: 'Unsupported File'
    };
  };

  // Detect if content is a Mermaid diagram
  const isMermaidContent = (content: string): boolean => {
    const trimmedContent = content.trim();

    // Check for common Mermaid diagram types
    const mermaidPatterns = [
      /^graph\s+(TD|TB|BT|RL|LR)/i,           // Flowchart
      /^flowchart\s+(TD|TB|BT|RL|LR)/i,       // Flowchart (new syntax)
      /^sequenceDiagram/i,                     // Sequence diagram
      /^classDiagram/i,                        // Class diagram
      /^stateDiagram/i,                        // State diagram
      /^erDiagram/i,                           // Entity relationship diagram
      /^gantt/i,                               // Gantt chart
      /^pie\s+title/i,                         // Pie chart
      /^journey/i,                             // User journey
      /^gitgraph/i,                            // Git graph
      /^mindmap/i,                             // Mind map
      /^timeline/i,                            // Timeline
      /^quadrantChart/i,                       // Quadrant chart
      /^requirement/i,                         // Requirement diagram
      /^C4Context/i,                           // C4 diagram
    ];

    // Check if content starts with any Mermaid pattern
    return mermaidPatterns.some(pattern => pattern.test(trimmedContent));
  };

  // Load generic file content (non-PDF files)
  const loadGenericFileContent = async (typeInfo: FileTypeInfo): Promise<void> => {
    if (!state.filePath) return;

    try {
      fileViewerService.setLoading(true);
      
      if (!window.electronAPI?.files?.getFileContent) {
        throw new Error('getFileContent API not available');
      }

      const result = await window.electronAPI.files.getFileContent(state.filePath);
      console.log('File getFileContent result:', result ? 'success' : 'failed', result?.length || 0);

      if (!result) {
        throw new Error('Empty or invalid file content received');
      }

      // Convert Buffer to string if needed
      const resultString = typeof result === 'string' ? result : result.toString('base64');
      
      if (resultString.trim() === '') {
        throw new Error('Empty file content after conversion');
      }

      if (typeInfo.type === 'image') {
        // For images, keep as base64
        setFileContent(resultString);
        console.log('🔄 [OVERLAY] Set fileContent for image:', resultString.length, 'bytes');
      } else {
        // For text-based files, decode from base64 with proper UTF-8 handling
        const textContent = decodeBase64ToUTF8(resultString);
        setFileContent(textContent);
        console.log('🔄 [OVERLAY] Set fileContent for text file:', textContent.length, 'characters');

        // Check if markdown content is actually a Mermaid diagram
        if (typeInfo.type === 'markdown' && isMermaidContent(textContent)) {
          const updatedTypeInfo = { ...typeInfo, type: 'mermaid' as const, displayName: 'Mermaid Diagram' };
          setFileTypeInfo(updatedTypeInfo);
        }
      }
    } catch (error) {
      console.error('Failed to load generic file content:', error);
      throw error;
    } finally {
      fileViewerService.setLoading(false);
    }
  };

  // Load PDF with PDF.js with proper preload logic
  const loadPDFWithPDFJS = async (): Promise<void> => {
    if (!state.filePath) return;

    try {
      fileViewerService.setLoading(true);
      setIsPdfReady(false);
      console.log('Loading PDF with PDF.js:', state.filePath);

      if (!window.electronAPI?.files?.getFileContent) {
        throw new Error('getFileContent API not available');
      }

      // Get file content as base64 for PDF.js
      const result = await window.electronAPI.files.getFileContent(state.filePath);
      console.log('PDF getFileContent result:', result ? 'success' : 'failed', result?.length || 0);
      
      if (!result) {
        throw new Error('Empty or invalid file content received');
      }

      // Convert Buffer to string if needed
      const resultString = typeof result === 'string' ? result : result.toString('base64');
      
      if (resultString.trim() === '') {
        throw new Error('Empty file content after conversion');
      }

      // Convert base64 to Uint8Array for PDF.js
      const binaryString = atob(resultString);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Load PDF document with PDF.js
      console.log('Loading PDF document with PDF.js...');
      const loadingTask = pdfjsLib.getDocument({ data: bytes });
      const pdf = await loadingTask.promise;
      
      console.log('PDF.js loaded successfully:', pdf.numPages, 'pages');
      setPdfDocument(pdf);
      setTotalPages(pdf.numPages);
      setCurrentPage(1);

      // Preload first page to ensure it's ready for rendering
      console.log('Preloading first page...');
      const firstPage = await pdf.getPage(1);
      await firstPage.getOperatorList(); // This ensures the page is fully loaded
      
      console.log('First page preloaded successfully');
      setIsPdfReady(true);

      // Small delay to ensure React state updates and canvas is ready
      setTimeout(async () => {
        console.log('Rendering first page after preload...');
        await renderPage(pdf, 1);
        fileViewerService.setLoading(false);
      }, 100);

    } catch (error) {
      console.error('Failed to load PDF with PDF.js:', error);
      setIsPdfReady(false);
      fileViewerService.setLoading(false);
      throw error;
    }
  };

  // Main file content loader
  const loadFileContent = async (typeInfo: FileTypeInfo): Promise<void> => {
    if (!state.filePath) return;

    console.log('Loading file content for:', state.filePath, 'Type:', typeInfo.type);

    try {
      if (typeInfo.type === 'pdf') {
        await loadPDFWithPDFJS();
      } else {
        await loadGenericFileContent(typeInfo);
      }
    } catch (error) {
      console.error('Failed to load file content:', error);
      // Error is already handled in the specific loaders
    }
  };

  // Render PDF page with proper error handling
  const renderPage = async (pdf: any, pageNumber: number): Promise<void> => {
    if (!pdf || !canvasRef.current) {
      console.log('Cannot render: missing pdf or canvas ref');
      return;
    }

    try {
      console.log(`Starting to render page ${pageNumber}`);
      const page = await pdf.getPage(pageNumber);
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');
      
      if (!context) {
        throw new Error('Cannot get canvas context');
      }

      // Calculate viewport and scale
      const viewport = page.getViewport({ scale: pdfState.zoom / 100 });
      canvas.height = viewport.height;
      canvas.width = viewport.width;

      // Render the page
      const renderContext = {
        canvasContext: context,
        viewport: viewport
      };

      console.log(`Rendering page ${pageNumber} at ${pdfState.zoom}% zoom`);
      await page.render(renderContext).promise;
      console.log(`Page ${pageNumber} rendered successfully`);

    } catch (error) {
      console.error(`Failed to render page ${pageNumber}:`, error);
      throw error;
    }
  };

  // Event handlers
  const handleClose = () => {
    fileViewerService.closeFile();
    onClose();
  };

  const handleZoomIn = () => {
    const newZoom = Math.min(pdfState.zoom + 25, 400);
    pdfViewerService.setZoom(newZoom);
    if (pdfDocument && currentPage) {
      renderPage(pdfDocument, currentPage);
    }
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(pdfState.zoom - 25, 25);
    pdfViewerService.setZoom(newZoom);
    if (pdfDocument && currentPage) {
      renderPage(pdfDocument, currentPage);
    }
  };

  // Removed unused functions: _resetZoom, _toggleEditMode, _saveFile

  const getFileIcon = () => {
    if (!fileTypeInfo) return ICONS.file;
    
    switch (fileTypeInfo.type) {
      case 'pdf': return ICONS.filePdf;
      case 'markdown': return ICONS.fileLines;
      case 'image': return ICONS.fileImage;
      case 'code': return ICONS.fileCode;
      default: return ICONS.file;
    }
  };

  // Mermaid renderer component
  const MermaidRenderer: React.FC<{ content: string }> = ({ content }) => {
    const mermaidRef = useRef<HTMLDivElement>(null);

    const renderMermaid = async () => {
      if (!mermaidRef.current) return;

      try {
        // Import mermaid with proper error handling
        const mermaidModule = await import('mermaid');
        const mermaid = mermaidModule.default || mermaidModule;
        
        mermaid.initialize({
          startOnLoad: false,
          theme: 'dark',
          securityLevel: 'loose'
        });

        const { svg } = await mermaid.render('mermaid-diagram', content);
        mermaidRef.current.innerHTML = svg;
      } catch (error) {
        console.error('Failed to render Mermaid diagram:', error);
        mermaidRef.current.innerHTML = `<pre class="text-red-400">Failed to render diagram: ${error}</pre>`;
      }
    };

    useEffect(() => {
      renderMermaid();
    }, [content]);

    return (
      <div className="bg-white rounded-lg p-4 overflow-auto">
        <div ref={mermaidRef} className="mermaid-diagram"></div>
      </div>
    );
  };

  // File content renderer
  const renderFileContent = () => {
    if (!fileTypeInfo) return null;

    switch (fileTypeInfo.type) {
      case 'pdf':
        return renderPDFContent();
      case 'markdown':
        return renderMarkdownContent();
      case 'mermaid':
        return renderMermaidContent();
      case 'text':
      case 'code':
        return renderTextContent();
      case 'image':
        return renderImageContent();
      default:
        return renderUnsupportedContent();
    }
  };

  const renderPDFContent = () => (
    <div className="flex flex-col items-center justify-center h-full">
      {!isPdfReady ? (
        <div className="text-center text-gray-400">
          <FontAwesomeIcon icon={ICONS.spinner} className="text-4xl mb-4 animate-spin" />
          <p className="text-lg font-medium">Preparing PDF...</p>
          <p className="text-sm">Loading and processing document</p>
        </div>
      ) : (
        <div className="relative w-full h-full flex flex-col">
          <canvas
            ref={canvasRef}
            className="mx-auto border border-gray-600 rounded-lg shadow-lg"
            style={{ maxWidth: '100%', maxHeight: 'calc(100vh - 200px)' }}
          />
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-4 mt-4 p-2 bg-gray-800 rounded-lg">
              <button
                onClick={() => handlePrevPage()}
                disabled={currentPage <= 1}
                className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 transition-colors"
              >
                <FontAwesomeIcon icon={ICONS.chevronLeft} className="text-sm" />
              </button>
              <span className="text-sm text-gray-300">
                Page {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => handleNextPage()}
                disabled={currentPage >= totalPages}
                className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 transition-colors"
              >
                <FontAwesomeIcon icon={ICONS.chevronRight} className="text-sm" />
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );

  const renderMarkdownContent = () => {
    const htmlContent = renderMarkdownToHTML(fileContent);
    return (
      <div className="markdown-body mx-auto p-6 pt-8 max-w-3xl">
        <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
      </div>
    );
  };

  const renderMermaidContent = () => (
    <div className="p-6 pt-8">
      <MermaidRenderer content={fileContent} />
    </div>
  );

  const renderTextContent = () => {
    if (isEditMode) {
      return (
        <div className="p-6 pt-8">
          <textarea
            value={editedContent}
            onChange={(e) => setEditedContent(e.target.value)}
            className="w-full h-full bg-gray-800 text-gray-200 p-4 rounded-lg border border-gray-600 focus:border-primary/50 focus:outline-none resize-none font-mono text-sm"
          />
        </div>
      );
    }

    return (
      <div className="p-6 pt-8">
        <pre className="text-gray-200 font-mono text-sm whitespace-pre-wrap overflow-auto h-full">
          {fileContent}
        </pre>
      </div>
    );
  };

  const renderImageContent = () => (
    <div className="flex items-start justify-center h-full p-6 pt-8">
      <img
        src={`data:${fileTypeInfo.mimeType};base64,${fileContent}`}
        alt="File content"
        className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
      />
    </div>
  );

  const renderUnsupportedContent = () => (
    <div className="flex items-start justify-center h-full pt-8">
      <div className="text-center text-gray-400">
        <FontAwesomeIcon icon={ICONS.file} className="text-6xl mb-4" />
        <h3 className="text-xl font-semibold mb-2">Unsupported File Type</h3>
        <p className="text-sm">Cannot preview .{fileTypeInfo?.extension} files</p>
      </div>
    </div>
  );

  // Actions
  const handleAskAI = async (): Promise<void> => {
    try {
      if (!state.filePath || !state.fileName) return;
      const vaultPath = extractContextPath(state.filePath) || '';

      // Prefer selected ideas; fallback to auto-selected; else just open chat with file
      const selectedIdeaTexts = (fileIntelligence?.key_ideas || [])
        .filter((idea) => idea.user_confirmed || idea.auto_selected)
        .map((idea) => idea.text);

      if (selectedIdeaTexts.length > 0) {
        await askAINavigationService.navigateToChatWithEntities(
          state.filePath,
          state.fileName,
          vaultPath,
          selectedIdeaTexts
        );
      } else {
        await askAINavigationService.navigateToChat(
          state.filePath,
          state.fileName,
          vaultPath
        );
      }

      askAINavigationService.showNavigationSuccess(state.fileName);
    } catch (error) {
      console.error('Failed to navigate to Ask AI:', error);
    }
  };

  const handleExtractText = async (): Promise<void> => {
    try {
      if (!state.filePath) return;

      let textToCopy = '';

      if (fileTypeInfo?.type === 'pdf' || fileTypeInfo?.type === 'image') {
        if (window.electronAPI?.files?.processFile) {
          const ext = fileTypeInfo.extension;
          try {
            const result = await window.electronAPI.files.processFile(state.filePath, ext);
            if (result?.success && result?.content?.text) {
              textToCopy = result.content.text as string;
            }
          } catch (e) {
            console.warn('processFile failed, falling back:', e);
          }
        }
      }

      // Fallbacks
      if (!textToCopy && (fileTypeInfo?.type === 'text' || fileTypeInfo?.type === 'code' || fileTypeInfo?.type === 'markdown')) {
        textToCopy = fileContent || '';
      }

      if (!textToCopy) {
        // As a last resort, attempt raw read via vault API
        if (window.electronAPI?.vault?.readFile) {
          const readResult = await window.electronAPI.vault.readFile(state.filePath);
          if (readResult?.success && typeof readResult.content === 'string') {
            textToCopy = readResult.content;
          }
        }
      }

      if (textToCopy) {
        await navigator.clipboard.writeText(textToCopy);
        // Simple user feedback without external deps
        console.log('✅ Text extracted and copied to clipboard. Length:', textToCopy.length);
        alert('Text extracted and copied to clipboard.');
      } else {
        alert('No extractable text found.');
      }
    } catch (error) {
      console.error('Failed to extract and copy text:', error);
      alert('Failed to extract text.');
    }
  };

  // V02 UNIFIED: Handle example prompt click - populate textarea and focus
  const handleExamplePromptClick = (promptText: string): void => {
    console.log('[ANNOTATIONS] 🎯 Example prompt clicked:', promptText);

    // Set the prompt text in the textarea
    setCustomPromptText(promptText);

    // Focus the textarea
    if (promptTextareaRef.current) {
      promptTextareaRef.current.focus();
      // Move cursor to end of text
      setTimeout(() => {
        if (promptTextareaRef.current) {
          const length = promptText.length;
          promptTextareaRef.current.setSelectionRange(length, length);
        }
      }, 0);
    }
  };

  // V02 UNIFIED: Handle prompt submission - record prompt + generate AI response
  const handleSubmitPrompt = async (): Promise<void> => {
    if (!state.filePath || !customPromptText.trim()) {
      alert('Please enter a prompt before submitting.');
      return;
    }

    console.log('[ANNOTATIONS] 🚀 Submitting prompt:', customPromptText);
    setIsProcessingAnnotation(true);

    try {
      // Generate AI annotation using the storage service
      const annotation = await annotationStorageService.generateAnnotation(
        state.filePath,
        customPromptText.trim(),
        fileContent
      );

      if (annotation) {
        // Save the annotation
        const saved = await annotationStorageService.saveAnnotation(state.filePath, annotation);

        if (saved) {
          // Reload annotations to update display
          await loadAnnotations();

          // Clear the prompt text
          setCustomPromptText('');

          console.log('[ANNOTATIONS] 🚀 ✅ Prompt submitted and annotation saved');
        } else {
          alert('Failed to save annotation. Please try again.');
        }
      } else {
        alert('Failed to generate annotation. Please try again.');
      }
    } catch (error) {
      console.error('[ANNOTATIONS] 🚀 ❌ Error submitting prompt:', error);
      alert('Error processing prompt. Please try again.');
    } finally {
      setIsProcessingAnnotation(false);
    }
  };

  // V02 UNIFIED: Load annotations for current file
  const loadAnnotations = async (): Promise<void> => {
    if (!state.filePath) return;

    try {
      console.log('[ANNOTATIONS] 📖 Loading annotations for:', state.filePath);
      const loadedAnnotations = await annotationStorageService.loadAnnotations(state.filePath);
      setAnnotations(loadedAnnotations);

      // Set current index to latest annotation
      if (loadedAnnotations.length > 0) {
        setCurrentAnnotationIndex(loadedAnnotations.length - 1);
      }

      console.log('[ANNOTATIONS] 📖 ✅ Loaded', loadedAnnotations.length, 'annotations');
    } catch (error) {
      console.error('[ANNOTATIONS] 📖 ❌ Error loading annotations:', error);
    }
  };

  // V02 UNIFIED: Handle annotation editing
  const handleEditAnnotation = (): void => {
    if (annotations.length > 0 && annotations[currentAnnotationIndex]) {
      setEditedAnnotationContent(annotations[currentAnnotationIndex].content);
      setIsEditingAnnotation(true);
      console.log('[ANNOTATIONS] ✏️ Started editing annotation:', annotations[currentAnnotationIndex].id);
    }
  };

  // V02 UNIFIED: Handle annotation save
  const handleSaveAnnotation = async (): Promise<void> => {
    if (!state.filePath || !annotations[currentAnnotationIndex] || !isEditingAnnotation) return;

    try {
      console.log('[ANNOTATIONS] 💾 Saving annotation changes');

      const annotationId = annotations[currentAnnotationIndex].id;
      const updates = {
        content: editedAnnotationContent.trim(),
        updated_at: new Date().toISOString()
      };

      const success = await annotationStorageService.updateAnnotation(state.filePath, annotationId, updates);

      if (success) {
        // Reload annotations to reflect changes
        await loadAnnotations();
        setIsEditingAnnotation(false);
        setEditedAnnotationContent('');
        console.log('[ANNOTATIONS] 💾 ✅ Annotation saved successfully');
      } else {
        alert('Failed to save annotation. Please try again.');
      }
    } catch (error) {
      console.error('[ANNOTATIONS] 💾 ❌ Error saving annotation:', error);
      alert('Error saving annotation. Please try again.');
    }
  };

  // V02 UNIFIED: Handle cancel edit
  const handleCancelEdit = (): void => {
    setIsEditingAnnotation(false);
    setEditedAnnotationContent('');
    console.log('[ANNOTATIONS] ✏️ Cancelled annotation editing');
  };

  const handleSaveUserNote = async (): Promise<void> => {
    if (!state.filePath) return;
    const content = (customPromptText || '').trim();
    if (!content) {
      alert('Please enter some text before saving a note.');
      return;
    }

    try {
      const contextPath = extractContextPath(state.filePath);
      // Load existing to merge
      const apiResult: any = await intelligenceClient.read(state.filePath, contextPath)
      const existing = apiResult && apiResult.success !== false ? (apiResult.data || null) : null
      const existingAnnotations: SmartAnnotationNote[] = (existing as any)?.smart_annotations || [];

      const newNote: SmartAnnotationNote = {
        id: `note_user_${Date.now()}`,
        type: 'user',
        title: 'User Note',
        content,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        labels_snapshot: fileIntelligence?.key_ideas || []
      };

      // Merge into current in-memory intelligence
      const merged: FileIntelligence = {
        file_path: state.filePath,
        key_ideas: fileIntelligence?.key_ideas || [],
        weighted_entities: fileIntelligence?.weighted_entities || [],
        human_connections: fileIntelligence?.human_connections || [],
        processing_confidence: fileIntelligence?.processing_confidence || 0,
        analysis_metadata: fileIntelligence?.analysis_metadata || { processing_time_ms: 0, model_used: 'smart_labeling_interface', timestamp: new Date().toISOString() },
        created_at: fileIntelligence?.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
        smart_annotations: [newNote, ...existingAnnotations]
      };

      // V02 COMPLIANT: Let SmartLabelingInterface handle persistence
      // Notes will be persisted when SmartLabelingInterface processes the updated data
      setFileIntelligence(merged);
      setCustomPromptText('');
      alert('Note saved (persistence handled by SmartLabelingInterface).');
    } catch (error) {
      console.error('Failed to save user note:', error);
      alert('Failed to save note.');
    }
  };

  // Simple markdown to HTML renderer
  const renderMarkdownToHTML = (markdown: string): string => {
    let html = markdown;

    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold text-white mt-6 mb-3">$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold text-white mt-8 mb-4">$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold text-white mt-8 mb-6">$1</h1>');

    // Bold and italic
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-white">$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em class="italic text-gray-200">$1</em>');

    // Code blocks
    html = html.replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-700 p-4 rounded-lg my-4 overflow-x-auto"><code class="text-sm font-mono text-gray-100">$1</code></pre>');

    // Inline code
    html = html.replace(/`(.*?)`/g, '<code class="bg-gray-700 px-2 py-1 rounded text-sm font-mono text-gray-100">$1</code>');

    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-primary hover:text-primary/80 underline" target="_blank" rel="noopener noreferrer">$1</a>');

    // Lists
    html = html.replace(/^\* (.*$)/gim, '<li class="text-gray-200 mb-1">$1</li>');
    html = html.replace(/^- (.*$)/gim, '<li class="text-gray-200 mb-1">$1</li>');

    // Wrap lists
    html = html.replace(/(<li.*<\/li>)/gs, '<ul class="list-disc list-inside space-y-1 my-4 text-gray-200">$1</ul>');

    // Paragraphs
    html = html.replace(/\n\n/g, '</p><p class="text-gray-200 mb-4">');
    html = '<p class="text-gray-200 mb-4">' + html + '</p>';

    // Clean up empty paragraphs
    html = html.replace(/<p class="text-gray-200 mb-4"><\/p>/g, '');

    return html;
  };

  const handleNextPage = async () => {
    if (pdfDocument && currentPage < totalPages) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      await renderPage(pdfDocument, nextPage);
    }
  };

  const handlePrevPage = async () => {
    if (pdfDocument && currentPage > 1) {
      const prevPage = currentPage - 1;
      setCurrentPage(prevPage);
      await renderPage(pdfDocument, prevPage);
    }
  };

  // Effects
  useEffect(() => {
    const unsubscribe = fileViewerService.subscribe(setState);
    return unsubscribe;
  }, []);

  useEffect(() => {
    const unsubscribe = pdfViewerService.subscribe(setPdfState);
    return unsubscribe;
  }, []);

  useEffect(() => {
    console.log('[ANNOTATIONS] 🔍 OVERLAY: useEffect triggered', {
      isOpen: state.isOpen,
      filePath: state.filePath,
      fileName: state.fileName
    });

    if (state.isOpen && state.filePath && state.fileName) {
      const typeInfo = detectFileType(state.fileName);
      setFileTypeInfo(typeInfo);
      console.log('🔄 [OVERLAY] Loading file content for:', state.filePath, 'Type:', typeInfo.type);
      loadFileContent(typeInfo);

      // SmartLabelingInterface is the single source of truth for intelligence
      // Don't clear state here - let SmartLabelingInterface manage it
      console.log('[LABELS] 🔄 OVERLAY: File opened, SmartLabelingInterface will manage intelligence state');
      
      // Only reset loading states, not the actual intelligence data
      setIsLoadingIntelligence(false);
      setIntelligenceError(null);
      console.log('[LABELS] 🔄 OVERLAY: Loading states reset, intelligence state preserved');

      // V02 UNIFIED: Load annotations for the opened file
      console.log('[ANNOTATIONS] 🚀 OVERLAY: About to load annotations for file:', state.filePath);
      loadAnnotations();
    }
  }, [state.filePath, state.isOpen, state.fileName]);

  if (!state.isOpen) return null;

  return (
    <div id="pdf-viewer-overlay" className="fixed inset-0 bg-gray-900 z-[9999] flex font-['Inter',sans-serif]">
      {/* Center Column - PDF Viewer */}
      <div id="pdf-viewer-panel" className="flex-1 bg-gray-800 flex flex-col border-r border-tertiary/50 min-w-0">
        {/* PDF Viewer Header */}
        <div className="p-4 border-b border-tertiary/50 flex items-center justify-between bg-gray-800/95 backdrop-blur-sm">
          <div className="flex items-center gap-3 min-w-0">
            <button onClick={handleClose} className="p-2.5 hover:bg-gray-700/80 rounded-lg transition-all duration-200 hover:scale-105 flex-shrink-0">
              <FontAwesomeIcon icon={ICONS.arrowLeft} className="text-gray-300 text-sm" />
            </button>
            <FontAwesomeIcon icon={getFileIcon()} className="text-secondary text-xl flex-shrink-0" />
            <span className="text-supplement1 font-semibold text-lg truncate">
              {state.fileName ? `${state.fileName.split('/').pop()?.split('\\').pop()} Intelligence Hub` : 'Intelligence Hub'}
            </span>
          </div>
          <div className="flex items-center gap-3 flex-shrink-0">
            <button onClick={handleZoomOut} className="p-2.5 hover:bg-gray-700/80 rounded-lg transition-all duration-200 hover:scale-105">
              <FontAwesomeIcon icon={ICONS.searchMinus} className="text-gray-300 text-sm" />
            </button>
            <span className="text-gray-300 text-sm font-medium bg-gray-700/50 px-3 py-1.5 rounded-md">{Math.round(fileTypeInfo?.type === 'pdf' ? pdfState.zoom : state.zoom)}%</span>
            <button onClick={handleZoomIn} className="p-2.5 hover:bg-gray-700/80 rounded-lg transition-all duration-200 hover:scale-105">
              <FontAwesomeIcon icon={ICONS.searchPlus} className="text-gray-300 text-sm" />
            </button>
            <button className="p-2.5 hover:bg-gray-700/80 rounded-lg transition-all duration-200 hover:scale-105">
              <FontAwesomeIcon icon={ICONS.download} className="text-gray-300 text-sm" />
            </button>
          </div>
        </div>
        
        {/* PDF Content */}
        <div id="pdf-content" className="flex-1 overflow-y-auto bg-gray-900 p-6 min-h-0">
          {state.isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-gray-400">
                <FontAwesomeIcon icon={ICONS.spinner} className="text-5xl mb-6 animate-spin text-primary" />
                <p className="text-xl font-semibold mb-2">Loading {fileTypeInfo?.displayName || 'File'}...</p>
                <p className="text-sm text-gray-500">Please wait while we load the document</p>
              </div>
            </div>
          ) : (
            <div className="h-full flex items-start justify-center">
              {renderFileContent()}
            </div>
          )}
        </div>
      </div>

      {/* Right Column - File Details */}
      <div id="file-details-panel" className="w-[28rem] bg-gray-800 flex flex-col overflow-hidden flex-shrink-0 relative group" style={{resize: "horizontal", minWidth: "360px", maxWidth: "700px"}}>
        {/* Resize Handle Indicator */}
        <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-transparent via-tertiary/40 to-transparent cursor-col-resize hover:bg-tertiary/60 transition-all duration-200 group-hover:bg-tertiary/50" />
        
        {/* Resize Feedback Overlay */}
        <div className="absolute inset-0 bg-tertiary/5 opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none" />
        
        {/* Resize Width Indicator */}
        <div className="absolute top-3 right-3 bg-gray-700/90 text-gray-300 text-xs px-2.5 py-1.5 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none font-medium">
          {Math.round(document.getElementById('file-details-panel')?.offsetWidth || 448)}px
        </div>
        
        {/* File Header */}
        <div className="p-3 border-b border-tertiary/50 flex items-center justify-between relative z-30 bg-gray-800/95 backdrop-blur-sm">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <FontAwesomeIcon icon={getFileIcon()} className="text-secondary text-lg flex-shrink-0" />
            <span className="text-supplement1 font-semibold text-sm truncate">
              {state.fileName ? `${state.fileName.split('/').pop()?.split('\\').pop()} Intelligence Hub` : 'Intelligence Hub'}
            </span>
          </div>
          <div className="flex items-center gap-1.5 ml-2 flex-shrink-0">
            <button className="p-1.5 hover:bg-gray-700/80 rounded-lg transition-all duration-200 hover:scale-105 group relative">
              <FontAwesomeIcon icon={ICONS.lock} className="text-red-400 text-xs" />
              <div className="absolute bottom-8 right-0 bg-gray-700/95 text-xs px-2 py-1 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 whitespace-nowrap z-10 backdrop-blur-sm">
                Private Document
              </div>
            </button>
            <button className="p-1.5 hover:bg-gray-700/80 rounded-lg transition-all duration-200 hover:scale-105 group relative">
              <FontAwesomeIcon icon={ICONS.ellipsisVertical} className="text-gray-400 text-xs" />
              <div className="absolute bottom-8 right-0 bg-gray-700/95 text-xs px-2 py-1 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 whitespace-nowrap z-10 backdrop-blur-sm">
                More Options
              </div>
            </button>
            <button onClick={handleClose} className="p-1.5 hover:bg-gray-700/80 rounded-lg transition-all duration-200 hover:scale-105">
              <FontAwesomeIcon icon={ICONS.xmark} className="text-gray-400 text-xs" />
            </button>
          </div>
        </div>
        
        {/* Smart Labeling Interface */}
        <div className="border-b border-tertiary/30">
          <SmartLabelingInterface
            filePath={state.filePath || ''}
            fileContent={fileContent}
            onLabelsChanged={handleLabelsChanged}
            onProcessingComplete={handleProcessingComplete}
          />
        </div>

        {/* V02 UNIFIED: Annotation Display Section */}
        {annotations.length > 0 && (
          <div className="p-3 border-b border-tertiary/30 flex-1 flex flex-col min-h-0">
            {/* Annotation Header with Pagination */}
            <div className="flex items-center justify-between mb-3 flex-shrink-0">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon icon={ICONS.stickyNote} className="text-supplement2 text-sm" />
                <h4 className="text-supplement1 font-semibold text-sm">
                  Annotation #{annotations[currentAnnotationIndex]?.note_number || 1}
                </h4>
                {/* Edit icon moved here */}
                <button
                  onClick={handleEditAnnotation}
                  className="p-1 hover:bg-gray-700/80 rounded-lg transition-all duration-200 hover:scale-105"
                  title="Edit annotation"
                >
                  <FontAwesomeIcon icon={ICONS.edit} className="text-gray-400 text-sm" />
                </button>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-400 text-xs font-medium bg-gray-700/50 px-2 py-1 rounded-md">
                  {currentAnnotationIndex + 1} of {annotations.length}
                </span>
                <button
                  onClick={() => setCurrentAnnotationIndex(Math.max(0, currentAnnotationIndex - 1))}
                  disabled={currentAnnotationIndex === 0}
                  className="p-1 hover:bg-gray-700/80 rounded-lg transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                >
                  <FontAwesomeIcon icon={ICONS.chevronLeft} className="text-gray-400 text-xs" />
                </button>
                <button
                  onClick={() => setCurrentAnnotationIndex(Math.min(annotations.length - 1, currentAnnotationIndex + 1))}
                  disabled={currentAnnotationIndex === annotations.length - 1}
                  className="p-1 hover:bg-gray-700/80 rounded-lg transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                >
                  <FontAwesomeIcon icon={ICONS.chevronRight} className="text-gray-400 text-xs" />
                </button>
              </div>
            </div>

            {/* Current Annotation Display */}
            {annotations[currentAnnotationIndex] && (
              <div className="bg-gray-900/60 border border-tertiary/40 rounded-lg p-3 shadow-lg flex-shrink-0">
                {/* Annotation Header - Simplified since edit icon moved up */}
                <div className="flex items-start justify-between mb-3 flex-shrink-0">
                  {isEditingAnnotation && (
                    <div className="flex items-center gap-2">
                      <FontAwesomeIcon
                        icon={annotations[currentAnnotationIndex].type === 'ai' ? ICONS.robot : ICONS.user}
                        className={`text-sm ${annotations[currentAnnotationIndex].type === 'ai' ? 'text-primary' : 'text-supplement2'}`}
                      />
                      <span className={`text-sm font-semibold ${annotations[currentAnnotationIndex].type === 'ai' ? 'text-primary' : 'text-supplement2'}`}>
                        {annotations[currentAnnotationIndex].type === 'ai' ? 'AI Response' : 'User Note'}
                      </span>
                    </div>
                  )}
                  <div className="flex items-center gap-1.5">
                    {isEditingAnnotation ? (
                      <>
                        <button
                          onClick={handleSaveAnnotation}
                          className="p-1.5 hover:bg-green-600/20 rounded-lg transition-all duration-200 hover:scale-105"
                          title="Save changes"
                        >
                          <FontAwesomeIcon icon={ICONS.save} className="text-green-400 text-sm" />
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          className="p-1.5 hover:bg-red-600/20 rounded-lg transition-all duration-200 hover:scale-105"
                          title="Cancel editing"
                        >
                          <FontAwesomeIcon icon={ICONS.times} className="text-red-400 text-sm" />
                        </button>
                      </>
                    ) : (
                      // Spacer to maintain layout when not editing
                      <div className="w-6 h-6"></div>
                    )}
                  </div>
                </div>

                {/* User Prompt (if exists) */}
                {annotations[currentAnnotationIndex].user_prompt && (
                  <div className="mb-3 p-2.5 bg-gray-800/60 border border-gray-600/50 rounded-lg flex-shrink-0">
                    <p className="text-gray-400 text-xs mb-1.5 font-medium">Prompt:</p>
                    <p className="text-gray-300 text-sm italic">"{annotations[currentAnnotationIndex].user_prompt}"</p>
                  </div>
                )}

                {/* Annotation Content - More compact height */}
                <div className="mb-3 flex-1 flex flex-col min-h-0">
                  {isEditingAnnotation ? (
                    <div className="relative flex-1 flex flex-col min-h-0">
                      <textarea
                        value={editedAnnotationContent}
                        onChange={(e) => setEditedAnnotationContent(e.target.value)}
                        className="flex-1 w-full bg-gray-800/80 border border-gray-600/50 rounded-lg p-2.5 text-sm text-gray-300 resize-none focus:outline-none focus:border-primary/50 focus:ring-2 focus:ring-primary/20 transition-all duration-200 font-mono overflow-y-auto"
                        placeholder="Edit annotation content..."
                        style={{ 
                          minHeight: '300px'
                        }}
                      />
                      {/* Markdown preview behind textarea */}
                      <div className="absolute inset-0 pointer-events-none">
                        <div className="p-2.5 text-sm text-gray-500 font-mono opacity-30">
                          {editedAnnotationContent || 'Start typing your annotation...'}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex-1 flex flex-col min-h-0">
                      <div className="overflow-y-auto bg-gray-800/40 rounded-lg p-2.5 border border-gray-700/50 flex-1" style={{ 
                        minHeight: '300px'
                      }}>
                        <p className="text-gray-300 text-sm leading-relaxed whitespace-pre-wrap">
                          {annotations[currentAnnotationIndex].content}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Custom Prompt Input Section */}
                <div className="pt-3 border-t border-gray-700/50 flex-shrink-0">
                  {/* Example Prompts - Lightbulb button with left-attached layer */}
                  {/* Removed: showAllPrompts and examplePrompts are now in the button row */}

                  {/* Custom Prompt Input */}
                  <div className="mb-3">
                    <textarea
                      ref={promptTextareaRef}
                      value={customPromptText}
                      onChange={(e) => setCustomPromptText(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && e.ctrlKey) {
                          e.preventDefault();
                          handleSubmitPrompt();
                        }
                      }}
                      className="w-full bg-gray-800/80 border border-gray-600/50 rounded-lg p-2.5 text-sm text-gray-300 resize-none focus:outline-none focus:border-primary/50 focus:ring-2 focus:ring-primary/20 transition-all duration-200"
                      placeholder="Enter your custom annotation prompt here..."
                      style={{ 
                        height: calculateTextAreaHeight(0.08, 50, 70)
                      }}
                    />
                  </div>

                  {/* Button Row - Lightbulb + Smart Annotation */}
                  <div className="flex items-center gap-3 mb-3">
                    {/* Lightbulb Button */}
                    <button
                      onClick={() => setShowAllPrompts(!showAllPrompts)}
                      className="w-12 h-12 bg-gray-800/60 hover:bg-gray-800/80 border border-gray-600/50 hover:border-gray-500 rounded-lg text-gray-300 transition-all duration-200 flex items-center justify-center group relative flex-shrink-0"
                      title="Example Prompts - Click to show/hide"
                    >
                      <FontAwesomeIcon icon={ICONS.lightbulb} className="text-yellow-400 text-lg" />
                      
                      {/* Left-attached example prompts layer */}
                      {showAllPrompts && (
                        <div className="absolute left-0 top-full mt-2 z-50">
                          <div className="bg-gray-900/95 border border-gray-700/50 rounded-lg p-3 shadow-xl backdrop-blur-sm min-w-64">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-gray-300">Example Prompts</span>
                              <button
                                onClick={() => setShowAllPrompts(false)}
                                className="text-gray-400 hover:text-gray-300 transition-colors"
                              >
                                <FontAwesomeIcon icon={ICONS.times} className="text-xs" />
                              </button>
                            </div>
                            <div className="space-y-1.5">
                              {examplePrompts.map((prompt, index) => (
                                <button
                                  key={index}
                                  onClick={() => {
                                    handleExamplePromptClick(prompt);
                                    setShowAllPrompts(false);
                                  }}
                                  className="w-full text-left p-2.5 bg-gray-800/60 hover:bg-gray-800/80 rounded-lg text-sm text-gray-300 border border-tertiary/40 hover:border-tertiary/60 transition-all duration-200 leading-tight hover:scale-[1.02]"
                                >
                                  "{prompt}"
                                </button>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </button>

                    {/* Smart Annotation Button - Reference Design Style */}
                    <div className="flex-1">
                      <button
                        onClick={handleSubmitPrompt}
                        disabled={!customPromptText.trim() || isProcessingAnnotation}
                        className="w-full bg-primary hover:bg-primary/80 text-gray-900 font-semibold py-2 px-3 rounded-lg transition-colors flex flex-col items-center justify-center gap-1"
                      >
                        <div className="flex items-center gap-2">
                          <FontAwesomeIcon icon={ICONS.wandMagicSparkles} className="text-sm" />
                          <span className="text-sm">Smart Annotation</span>
                        </div>
                        <span className="text-xs text-gray-700">
                          Prompt AI or write your own thoughts
                        </span>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Timestamp */}
                <div className="flex justify-between items-center mt-3 pt-2 border-t border-gray-700/50 flex-shrink-0">
                  <span className="text-gray-500 text-xs font-medium">
                    {new Date(annotations[currentAnnotationIndex].created_at).toLocaleString()}
                  </span>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Unified Action Buttons Section */}
        <div className="p-3 border-t border-tertiary/50 mt-auto bg-gray-800/95 backdrop-blur-sm">
          {/* Action Buttons */}
          <div className="grid grid-cols-2 gap-2.5">
            {/* Ask AI Button */}
            <div className="flex flex-col">
              <button onClick={handleAskAI} className="bg-secondary/10 hover:bg-secondary/20 border border-secondary/40 hover:border-secondary/60 text-secondary font-semibold py-2.5 px-3 rounded-lg transition-all duration-200 text-sm flex items-center justify-center gap-2 hover:scale-105 group">
                <FontAwesomeIcon icon={ICONS.comments} className="text-sm" />
                <span>Ask AI</span>
                <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-gray-700/95 text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 whitespace-nowrap z-10 backdrop-blur-sm">
                  Send to chat to explore more with other LLM models
                </div>
              </button>
              <p className="text-xs text-gray-500 mt-1.5 text-center">Send to chat to explore more with other LLM models</p>
            </div>
            
            {/* Extract Text Button */}
            <div className="flex flex-col">
              <button onClick={handleExtractText} className="bg-supplement2/10 hover:bg-supplement2/20 border border-supplement2/40 hover:border-supplement2/60 text-supplement2 font-semibold py-2.5 px-3 rounded-lg transition-all duration-200 text-sm flex items-center justify-center gap-2 hover:scale-105 group">
                <FontAwesomeIcon icon={ICONS.arrowsRotate} className="text-sm" />
                <span>Extract Text</span>
                <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-gray-700/95 text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 whitespace-nowrap z-10 backdrop-blur-sm">
                  OCR the text and copy to the clipboard
                </div>
              </button>
              <p className="text-xs text-gray-500 mt-1.5 text-center">OCR the text and copy to the clipboard</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
