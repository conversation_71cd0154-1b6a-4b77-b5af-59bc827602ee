import { useEffect, useRef, useCallback } from 'react'
import { performanceMonitor } from '../services/performanceMonitor'

interface PerformanceMetrics {
  loadTime: number
  renderTime: number
  cacheHit: boolean
  vaultCount: number
}

/**
 * Hook for tracking performance metrics in components
 * Automatically measures load times and reports to performance monitor
 */
export function usePerformanceTracking(componentName: string) {
  const startTime = useRef<number>(Date.now())
  const renderStartTime = useRef<number>(0)
  const metrics = useRef<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    cacheHit: false,
    vaultCount: 0
  })

  // Start timing when component mounts
  useEffect(() => {
    startTime.current = Date.now()
    renderStartTime.current = performance.now()
    
    console.log(`🚀 [PERF] ${componentName} mounting started`)
    
    return () => {
      const totalTime = Date.now() - startTime.current
      console.log(`✅ [PERF] ${componentName} lifecycle completed in ${totalTime}ms`)
      
      // Report final metrics
      if (metrics.current.loadTime > 0) {
        performanceMonitor.recordProcessingTime('quick', totalTime)
      }
    }
  }, [componentName])

  // Track render performance
  useEffect(() => {
    const renderTime = performance.now() - renderStartTime.current
    metrics.current.renderTime = renderTime
    
    if (renderTime > 16) { // 16ms = 60fps threshold
      console.warn(`⚠️ [PERF] ${componentName} render took ${renderTime.toFixed(2)}ms (target: <16ms)`)
    }
  })

  // Track data loading performance
  const trackDataLoad = useCallback((cacheHit: boolean, vaultCount: number, loadTime: number) => {
    metrics.current = {
      ...metrics.current,
      loadTime,
      cacheHit,
      vaultCount
    }

    console.log(`📊 [PERF] ${componentName} data load:`, {
      cacheHit: cacheHit ? '✅ HIT' : '❌ MISS',
      vaultCount,
      loadTime: `${loadTime}ms`,
      performance: loadTime < 100 ? '🚀 FAST' : loadTime < 500 ? '⚡ GOOD' : '🐌 SLOW'
    })

    // Report to performance monitor
    performanceMonitor.recordProcessingTime('quick', loadTime)
  }, [componentName])

  // Track user interaction performance
  const trackInteraction = useCallback((action: string, duration: number) => {
    console.log(`👆 [PERF] ${componentName} interaction: ${action} took ${duration}ms`)
    
    if (duration > 100) {
      console.warn(`⚠️ [PERF] ${componentName} interaction ${action} was slow: ${duration}ms`)
    }
    
    performanceMonitor.recordProcessingTime('immediate', duration)
  }, [componentName])

  return {
    trackDataLoad,
    trackInteraction,
    getMetrics: () => metrics.current
  }
}

export default usePerformanceTracking
