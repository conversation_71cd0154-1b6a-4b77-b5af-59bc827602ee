import * as os from 'os'
import * as fs from 'fs'
import * as path from 'path'
import { FileCoreService } from '../FileCoreService'

async function run() {
  const tmp = await fs.promises.mkdtemp(path.join(os.tmpdir(), 'filecore-'))
  const svc = new FileCoreService()

  const txt = path.join(tmp, 'a.txt')
  await fs.promises.writeFile(txt, 'hello', 'utf8')
  const metaTxt = await svc.getMetadata(txt)
  if (metaTxt.mime !== 'text/plain') throw new Error(`Expected text/plain, got ${metaTxt.mime}`)

  const png = path.join(tmp, 'a.png')
  const pngHeader = Buffer.from('89504e470d0a1a0a', 'hex')
  await fs.promises.writeFile(png, Buffer.concat([pngHeader, Buffer.from('deadbeef','hex')]))
  const metaPng = await svc.getMetadata(png)
  if (metaPng.mime !== 'image/png') throw new Error(`Expected image/png, got ${metaPng.mime}`)

  console.log('FileCoreService MIME smoke tests passed')
}

run().catch(err => { console.error(err); process.exit(1) })
