import path from 'path'
import * as fs from 'fs'

/**
 * PathResolver
 * Centralized, platform-aware path helpers for core services
 */
export class PathResolver {
  static normalizePath(targetPath: string): string {
    return path.normalize(targetPath)
  }

  static joinSafe(...parts: string[]): string {
    return path.join(...parts)
  }

  static getContextDir(vaultPath: string): string {
    const normalizedVault = this.normalizePath(vaultPath)
    // Legacy method - prefer .intelligence but fallback to .context for compatibility
    return this.joinSafe(normalizedVault, '.intelligence')
  }

  static getContextFilesDir(vaultPath: string): string {
    return this.joinSafe(this.getContextDir(vaultPath), 'files')
  }

  static getFileNameWithoutExtension(filePath: string): string {
    const fileName = path.basename(filePath)
    const lastDotIndex = fileName.lastIndexOf('.')
    return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName
  }

  // Stable short hash used across the app for mapping a file to its metadata
  static computeStableHash(input: string): string {
    const normalized = this.normalizePath(input).replace(/\\/g, '/').toLowerCase()
    let hash = 0
    for (let i = 0; i < normalized.length; i++) {
      const c = normalized.charCodeAt(i)
      hash = ((hash << 5) - hash) + c
      hash = hash & hash
    }
    
    // Create a more readable filename from the original path
    const fileName = input.split(/[/\\]/).pop()?.replace(/\.[^.]*$/, '') || 'unknown'
    const hashSuffix = Math.abs(hash).toString(16).substring(0, 8)
    return `${fileName}_${hashSuffix}`
  }

  static getIntelligenceJsonPath(filePath: string, vaultPath: string): { filesDir: string; jsonPath: string; fileHash: string } {
    const normalizedFile = this.normalizePath(filePath)
    const normalizedVault = this.normalizePath(vaultPath)

    const fileHash = this.computeStableHash(normalizedFile)

    // LEGACY: Keep old structure for backward compatibility with intelligence:read/write
    // Note: V02 sessions use getIntelligenceSessionsDir() instead
    const filesDir = this.joinSafe(this.getIntelligenceBaseDir(normalizedVault), 'files')
    const jsonPath = this.joinSafe(filesDir, `${fileHash}.json`)

    return { filesDir, jsonPath, fileHash }
  }

  // Intelligence V02 storage layout: <vault>/.intelligence/documents/<hash>/sessions/
  static getIntelligenceBaseDir(vaultPath: string): string {
    return this.joinSafe(this.normalizePath(vaultPath), '.intelligence')
  }

  static getIntelligenceDocumentDir(filePath: string, vaultPath: string): string {
    const fileHash = this.computeStableHash(filePath)
    return this.joinSafe(this.getIntelligenceBaseDir(vaultPath), 'documents', fileHash)
  }

  static getIntelligenceSessionsDir(filePath: string, vaultPath: string): string {
    return this.joinSafe(this.getIntelligenceDocumentDir(filePath, vaultPath), 'sessions')
  }

  // Artifacts persistence contract
  static getArtifactsDir(filePath: string, vaultPath: string): string {
    return this.joinSafe(this.getIntelligenceDocumentDir(filePath, vaultPath), 'artifacts')
  }

  static getArtifactsPath(filePath: string, vaultPath: string): string {
    return this.joinSafe(this.getArtifactsDir(filePath, vaultPath), 'artifacts.json')
  }

  // Infer the vault/context directory from an absolute file path
  // Strategy:
  // 1) If path contains '/documents/', take everything before that token as vaultPath
  // 2) Otherwise, walk up to find a directory containing a '.intelligence' folder (or '.context' for legacy)
  // 3) If neither works, return null
  static inferVaultPath(filePath: string): string | null {
    try {
      const normalized = this.normalizePath(filePath).replace(/\\/g, '/')
      const token = '/documents/'
      const idx = normalized.lastIndexOf(token)
      if (idx !== -1) {
        const candidate = normalized.substring(0, idx)
        return this.normalizePath(candidate)
      }

      // Walk up to find a directory containing '.intelligence' (or fallback to '.context' for legacy)
      let current = path.dirname(this.normalizePath(filePath))
      const root = path.parse(current).root
      while (current && current !== root) {
        const intelligenceDir = this.joinSafe(current, '.intelligence')
        const contextDir = this.joinSafe(current, '.context')
        if (fs.existsSync(intelligenceDir) && fs.lstatSync(intelligenceDir).isDirectory()) {
          return this.normalizePath(current)
        }
        // Legacy fallback for existing vaults
        if (fs.existsSync(contextDir) && fs.lstatSync(contextDir).isDirectory()) {
          return this.normalizePath(current)
        }
        current = path.dirname(current)
      }
      return null
    } catch {
      return null
    }
  }

  // Relative path helper
  static getRelativePath(basePath: string, absolutePath: string): string {
    return path.relative(this.normalizePath(basePath), this.normalizePath(absolutePath))
  }

  // Generate a unique filename in a directory by appending (n) before the extension
  static async generateUniqueFilename(dir: string, desiredName: string): Promise<string> {
    const normalizedDir = this.normalizePath(dir)
    const ext = path.extname(desiredName)
    const nameWithoutExt = path.basename(desiredName, ext)
    let candidate = desiredName
    let counter = 1
    while (true) {
      const full = this.joinSafe(normalizedDir, candidate)
      const exists = await fs.promises.access(full).then(() => true).catch(() => false)
      if (!exists) return candidate
      candidate = `${nameWithoutExt} (${counter})${ext}`
      counter += 1
    }
  }
}
