import * as fs from 'fs'
import * as path from 'path'
import { createHash } from 'crypto'

import { PathResolver } from './PathResolver'
import { VaultCoreService } from './VaultCoreService'
import { EventBus } from './EventBus'
import { pluginFileProcessor } from '../fileProcessors/PluginFileProcessor'

export interface FileDescriptor {
  vaultPath: string
  filePath: string
  relativePath?: string
  size: number
  modified: string
  contentHash: string
  mime: string
}

export interface ProcessedArtifacts {
  text?: string
  metadata?: Record<string, any>
  error?: string
}

export class FileCoreService {
  private vault: VaultCoreService
  private metadataCache: Map<string, FileDescriptor> = new Map()
  private artifactsCache: Map<string, ProcessedArtifacts> = new Map() // keyed by contentHash

  constructor() {
    this.vault = new VaultCoreService()
  }

  async indexFile(filePath: string, vaultPath?: string): Promise<FileDescriptor> {
    const normalizedFile = PathResolver.normalizePath(filePath)
    const stat = await fs.promises.stat(normalizedFile)
    const contentHash = await this.computeContentHash(normalizedFile)
    const mime = await this.sniffMime(normalizedFile)

    const descriptor: FileDescriptor = {
      vaultPath: vaultPath ? PathResolver.normalizePath(vaultPath) : '',
      filePath: normalizedFile,
      relativePath: vaultPath ? PathResolver.getRelativePath(PathResolver.normalizePath(vaultPath), normalizedFile) : undefined,
      size: stat.size,
      modified: stat.mtime.toISOString(),
      contentHash,
      mime
    }

    this.metadataCache.set(normalizedFile, descriptor)

    EventBus.broadcast('file:indexed', { ...descriptor, changeType: 'indexed' })
    return descriptor
  }

  async getMetadata(filePath: string): Promise<FileDescriptor> {
    const normalizedFile = PathResolver.normalizePath(filePath)
    const cached = this.metadataCache.get(normalizedFile)
    if (cached) return cached
    return this.indexFile(normalizedFile)
  }

  async reindexTree(vaultPath: string, relativeDir?: string, processContent = false): Promise<{ indexed: number; processed: number }> {
    const root = PathResolver.normalizePath(vaultPath)
    const startDir = relativeDir ? PathResolver.joinSafe(root, relativeDir) : root

    let indexed = 0
    let processed = 0

    const walk = async (dir: string) => {
      const entries = await this.vault.readDirectory(dir)
      for (const entry of entries) {
        if (entry.isDirectory) {
          // Skip hidden/system directories
          if (entry.name.startsWith('.')) continue
          await walk(entry.path)
        } else {
          indexed += 1
          await this.indexFile(entry.path, root)
          if (processContent) {
            const r = await this.processFile(entry.path)
            if (!r.error) processed += 1
          }
        }
      }
    }

    await walk(startDir)
    return { indexed, processed }
  }

  async processFile(filePath: string, fileType?: string, vaultPath?: string): Promise<ProcessedArtifacts> {
    const normalizedFile = PathResolver.normalizePath(filePath)
    const inferredType = fileType || await this.sniffMime(normalizedFile)

    const taskId = `process_${Date.now()}_${path.basename(normalizedFile)}`
    // Emit start event
    EventBus.broadcast('task:progress', {
      taskId,
      percent: 0,
      message: `Processing ${path.basename(normalizedFile)}…`
    })

    const result = await pluginFileProcessor.processFile(normalizedFile, inferredType)

    // Persist artifacts
    try {
      if (!result.error && (result.text || result.metadata)) {
        const artifactsDir = PathResolver.getArtifactsDir(normalizedFile, vaultPath || '')
        const artifactsPath = PathResolver.getArtifactsPath(normalizedFile, vaultPath || '')
        await fs.promises.mkdir(artifactsDir, { recursive: true })
        const payload = { text: result.text || '', metadata: result.metadata || {} }
        await fs.promises.writeFile(artifactsPath, JSON.stringify(payload, null, 2), 'utf8')
        // cache by content hash
        const descriptor = await this.getMetadata(normalizedFile)
        this.artifactsCache.set(descriptor.contentHash, payload)
      }
    } catch (e) {
      // ignore persistence errors for now
    }

    // Emit completion event
    EventBus.broadcast('file:processed', {
      path: normalizedFile,
      vaultPath,
      success: !result.error,
      error: result.error,
      changeType: 'processed'
    })

    EventBus.broadcast('task:progress', {
      taskId,
      percent: 100,
      message: `Processed ${path.basename(normalizedFile)}`
    })

    return {
      text: result.text,
      metadata: result.metadata,
      error: result.error
    }
  }

  // --- Private helpers
  private async computeContentHash(filePath: string): Promise<string> {
    const hash = createHash('sha256')
    const stream = fs.createReadStream(filePath)
    return new Promise<string>((resolve, reject) => {
      stream.on('data', chunk => hash.update(chunk))
      stream.on('end', () => resolve(hash.digest('hex')))
      stream.on('error', reject)
    })
  }

  private async sniffMime(filePath: string): Promise<string> {
    const ext = path.extname(filePath).toLowerCase()
    // Read first 8 bytes
    let header: Buffer | null = null
    try {
      const fd = await fs.promises.open(filePath, 'r')
      const buf = Buffer.alloc(8)
      await fd.read(buf, 0, 8, 0)
      await fd.close()
      header = buf
    } catch {}

    const headerHex = header ? header.toString('hex') : ''
    if (header) {
      if (header.slice(0, 4).toString() === '%PDF') return 'application/pdf'
      if (headerHex.startsWith('89504e470d0a1a0a')) return 'image/png'
      if (headerHex.startsWith('ffd8ff')) return 'image/jpeg'
      if (header.slice(0, 3).toString() === 'GIF') return 'image/gif'
      if (headerHex.startsWith('504b0304')) {
        // zip-based containers; disambiguate by extension
        if (ext === '.docx') return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        if (ext === '.xlsx') return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        if (ext === '.pptx') return 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        return 'application/zip'
      }
    }

    switch (ext) {
      case '.md':
        return 'text/markdown'
      case '.txt':
        return 'text/plain'
      case '.csv':
        return 'text/csv'
      case '.html':
      case '.htm':
        return 'text/html'
      case '.json':
        return 'application/json'
      case '.pdf':
        return 'application/pdf'
      case '.png':
        return 'image/png'
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg'
      case '.gif':
        return 'image/gif'
      case '.svg':
        return 'image/svg+xml'
      case '.doc':
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      case '.xls':
      case '.xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      case '.ppt':
      case '.pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
      default:
        return 'application/octet-stream'
    }
  }
}


