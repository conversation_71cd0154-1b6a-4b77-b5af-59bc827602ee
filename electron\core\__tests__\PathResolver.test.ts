import { PathResolver } from '../PathResolver'
import * as os from 'os'
import * as fs from 'fs'
import * as path from 'path'

async function run() {
  const tmp = await fs.promises.mkdtemp(path.join(os.tmpdir(), 'pathresolver-'))

  // generateUniqueFilename
  const dir = path.join(tmp, 'files')
  await fs.promises.mkdir(dir, { recursive: true })
  const base = 'report.txt'
  const first = await PathResolver.generateUniqueFilename(dir, base)
  if (first !== 'report.txt') throw new Error('Expected first unique name to be report.txt')
  await fs.promises.writeFile(path.join(dir, first), 'x', 'utf8')
  const second = await PathResolver.generateUniqueFilename(dir, base)
  if (second !== 'report (1).txt') throw new Error(`Expected second unique name to be report (1).txt, got ${second}`)

  // getRelativePath
  const baseDir = path.join(tmp, 'a', 'b')
  const filePath = path.join(baseDir, 'c', 'file.md')
  const rel = PathResolver.getRelativePath(baseDir, filePath)
  if (rel !== path.join('c', 'file.md')) throw new Error(`Unexpected relative path: ${rel}`)

  console.log('PathResolver tests passed')
}

run().catch(err => { console.error(err); process.exit(1) })
