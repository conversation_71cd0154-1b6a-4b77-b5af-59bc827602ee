import { app } from 'electron'
import * as fs from 'fs'
import * as path from 'path'
import * as os from 'os'
import * as crypto from 'crypto'
import { DatabaseManager, FileRecord } from './database'
import { FileProcessorService } from './fileProcessors'
import { isDev } from './utils'
import { PathResolver } from './core/PathResolver'

export class FileSystemManager {
  private dbManager: DatabaseManager
  private chatloFolderPath: string
  public fileProcessor: FileProcessorService

  constructor(dbManager: DatabaseManager) {
    this.dbManager = dbManager
    this.chatloFolderPath = this.resolveVaultRoot()
    this.fileProcessor = new FileProcessorService()
    console.log('🔧 FileSystemManager created with FileProcessorService')

    // Path audit (info-level) – migrating from legacy hardcoded path to vault-root-path
    console.log('[PATH] Using vault root path:', this.chatloFolderPath)
  }

  // Resolve the vault root path from settings, fallback to homedir/Documents/ChatLo_Vaults
  private resolveVaultRoot(): string {
    try {
      const configured = this.dbManager.getSetting('vault-root-path')
      if (configured && typeof configured === 'string') {
        return PathResolver.normalizePath(configured)
      }
    } catch {
      // ignore and use fallback
    }
    const fallback = path.join(os.homedir(), 'Documents', 'ChatLo_Vaults')
    return PathResolver.normalizePath(fallback)
  }

  // Initialize the default Chatlo folder structure
  /**
   * Initialize the vault root directory structure
   */
  async initializeVaultRoot(): Promise<void> {
    try {
      // Ensure vault root exists
      const vaultRoot = this.chatloFolderPath
      if (!fs.existsSync(vaultRoot)) {
        fs.mkdirSync(vaultRoot, { recursive: true })
        console.log('Created vault root at:', vaultRoot)
      }

      console.log('Vault root structure initialized')
    } catch (error) {
      console.error('Error initializing Chatlo folder:', error)
      throw error
    }
  }

  /**
   * @deprecated Use initializeVaultRoot()
   */
  async initializeChatloFolder(): Promise<void> {
    console.warn('[DEPRECATED] initializeChatloFolder() is deprecated. Use initializeVaultRoot().')
    return this.initializeVaultRoot()
  }

  // Get the vault root path
  getVaultRootPath(): string {
    return this.chatloFolderPath
  }

  /**
   * @deprecated Use getVaultRootPath()
   */
  getChatloFolderPath(): string {
    console.warn('[DEPRECATED] getChatloFolderPath() is deprecated. Use getVaultRootPath().')
    return this.getVaultRootPath()
  }

  // Set a new Chatlo folder path
  async setVaultRootPath(newPath: string): Promise<void> {
    try {
      // Validate the path exists
      if (!fs.existsSync(newPath)) {
        throw new Error('Selected folder does not exist')
      }

      // Update the path
      const normalized = PathResolver.normalizePath(newPath)
      this.chatloFolderPath = normalized

      // Persist as vault root in settings
      try {
        this.dbManager.setSetting('vault-root-path', normalized)
      } catch (e) {
        console.warn('Failed to persist vault-root-path setting:', (e as Error)?.message)
      }

      // Initialize the new folder structure
      await this.initializeVaultRoot()

      // Re-index all files in the new location
      await this.indexAllFiles()

      console.log('Chatlo folder path updated to:', newPath)
    } catch (error) {
      console.error('Error setting Chatlo folder path:', error)
      throw error
    }
  }

  /**
   * @deprecated Use setVaultRootPath(newPath)
   */
  async setChatloFolderPath(newPath: string): Promise<void> {
    console.warn('[DEPRECATED] setChatloFolderPath() is deprecated. Use setVaultRootPath().')
    return this.setVaultRootPath(newPath)
  }

  // Calculate file hash for deduplication
  public calculateFileHash(filePath: string): string {
    const fileBuffer = fs.readFileSync(filePath)
    return crypto.createHash('sha256').update(fileBuffer).digest('hex')
  }

  // Get file type from extension
  public getFileType(filename: string): string {
    const ext = path.extname(filename).toLowerCase()
    const typeMap: { [key: string]: string } = {
      '.pdf': 'pdf',
      '.doc': 'word',
      '.docx': 'word',
      '.xls': 'excel',
      '.xlsx': 'excel',
      '.ppt': 'powerpoint',
      '.pptx': 'powerpoint',
      '.txt': 'text',
      '.md': 'markdown',
      '.markdown': 'markdown',
      '.mdown': 'markdown',
      '.mkd': 'markdown',
      '.csv': 'text',
      '.json': 'text',
      '.xml': 'text',
      '.yaml': 'text',
      '.yml': 'text',
      '.ini': 'text',
      '.conf': 'text',
      '.rtf': 'text',
      '.html': 'html',
      '.htm': 'html',
      '.jpg': 'image',
      '.jpeg': 'image',
      '.png': 'image',
      '.gif': 'image',
      '.bmp': 'image',
      '.webp': 'image',
      '.svg': 'image',
      '.tiff': 'image',
      '.ico': 'image'
    }
    return typeMap[ext] || 'unknown'
  }

  // Get MIME type from extension
  public getMimeType(filename: string): string {
    const ext = path.extname(filename).toLowerCase()
    const mimeMap: { [key: string]: string } = {
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.ppt': 'application/vnd.ms-powerpoint',
      '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.markdown': 'text/markdown',
      '.mdown': 'text/markdown',
      '.mkd': 'text/markdown',
      '.csv': 'text/csv',
      '.json': 'application/json',
      '.xml': 'application/xml',
      '.yaml': 'text/yaml',
      '.yml': 'text/yaml',
      '.ini': 'text/plain',
      '.conf': 'text/plain',
      '.rtf': 'application/rtf',
      '.html': 'text/html',
      '.htm': 'text/html',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.bmp': 'image/bmp',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml',
      '.tiff': 'image/tiff',
      '.ico': 'image/x-icon'
    }
    return mimeMap[ext] || 'application/octet-stream'
  }

  // Index a vault file with vault-specific metadata
  async indexVaultFile(filePath: string, vaultName: string, relativePath: string, processContent: boolean = false): Promise<{success: boolean, file?: any, error?: string}> {
    try {
      if (!fs.existsSync(filePath)) {
        console.warn('Vault file does not exist:', filePath)
        return { success: false, error: 'File does not exist' }
      }

      const stats = fs.statSync(filePath)
      const filename = path.basename(filePath)
      const fileType = this.getFileType(filename)
      const mimeType = this.getMimeType(filename)
      const contentHash = this.calculateFileHash(filePath)

      // Check if vault file already exists in database
      const existingFile = this.dbManager.getFileByPath(filePath)
      if (existingFile && existingFile.content_hash === contentHash) {
        console.log('Vault file already indexed and unchanged:', filename)
        return { success: true, file: existingFile }
      }

      // Add or update vault file in database with vault-specific metadata
      const fileRecord: Omit<FileRecord, 'id' | 'created_at' | 'updated_at'> = {
        filename,
        filepath: filePath,
        file_type: fileType,
        file_size: stats.size,
        content_hash: contentHash,
        mime_type: mimeType,
        extracted_content: undefined, // Will be populated by file processors
        vault_name: vaultName,
        relative_path: relativePath,
        storage_type: 'vault',
        metadata: JSON.stringify({
          lastModified: stats.mtime.toISOString(),
          isDirectory: stats.isDirectory(),
          extension: path.extname(filename),
          vaultName: vaultName,
          relativePath: relativePath
        })
      }

      let fileId: string
      let updatedFile: any
      if (existingFile) {
        this.dbManager.updateFile(existingFile.id, fileRecord)
        console.log('Updated vault file index:', filename)
        fileId = existingFile.id
        updatedFile = { ...existingFile, ...fileRecord, id: fileId }
      } else {
        fileId = this.dbManager.addFile(fileRecord)
        console.log('Indexed new vault file:', filename)
        updatedFile = { ...fileRecord, id: fileId }
      }

      // Only process file content if explicitly requested (for chat usage)
      if (processContent && this.fileProcessor.isFileTypeSupportedSync(fileType)) {
        try {
          console.log('Processing vault file content for:', filename)
          const processedContent = await this.fileProcessor.processFile(filePath, fileType)

          if (processedContent.text && !processedContent.error) {
            // Update the file record with extracted content
            this.dbManager.updateFile(fileId, {
              extracted_content: processedContent.text,
              metadata: JSON.stringify({
                ...JSON.parse(fileRecord.metadata || '{}'),
                processed: true,
                processingMetadata: processedContent.metadata
              })
            })
            console.log('Vault file content processed successfully:', filename)
            
            // Reload the file record to get updated content
            updatedFile = this.dbManager.getFile(fileId)
          } else if (processedContent.error) {
            console.warn('Vault file processing failed:', filename, processedContent.error)
          }
        } catch (error) {
          console.error('Error processing vault file content:', filename, error)
        }
      } else if (!processContent) {
        console.log('Vault file indexed (metadata only):', filename)
      }

      return { success: true, file: updatedFile }
    } catch (error: any) {
      console.error('Error indexing vault file:', filePath, error)
      return { success: false, error: error.message }
    }
  }

  // Index a single file (metadata only by default, lazy content processing)
  async indexFile(filePath: string, processContent: boolean = false): Promise<string | null> {
    try {
      if (!fs.existsSync(filePath)) {
        console.warn('File does not exist:', filePath)
        return null
      }

      const stats = fs.statSync(filePath)
      const filename = path.basename(filePath)
      const fileType = this.getFileType(filename)
      const mimeType = this.getMimeType(filename)
      const contentHash = this.calculateFileHash(filePath)

      // Check if file already exists in database
      const existingFile = this.dbManager.getFileByPath(filePath)
      if (existingFile && existingFile.content_hash === contentHash) {
        console.log('File already indexed and unchanged:', filename)
        return existingFile.id
      }

      // Add or update file in database
      const fileRecord: Omit<FileRecord, 'id' | 'created_at' | 'updated_at'> = {
        filename,
        filepath: filePath,
        file_type: fileType,
        file_size: stats.size,
        content_hash: contentHash,
        mime_type: mimeType,
        extracted_content: undefined, // Will be populated by file processors
        metadata: JSON.stringify({
          lastModified: stats.mtime.toISOString(),
          isDirectory: stats.isDirectory(),
          extension: path.extname(filename)
        })
      }

      let fileId: string
      if (existingFile) {
        this.dbManager.updateFile(existingFile.id, fileRecord)
        console.log('Updated file index:', filename)
        fileId = existingFile.id
      } else {
        fileId = this.dbManager.addFile(fileRecord)
        console.log('Indexed new file:', filename)
      }

      // Only process file content if explicitly requested (for chat usage)
      if (processContent && this.fileProcessor.isFileTypeSupportedSync(fileType)) {
        try {
          console.log('=== DEBUG: Starting file processing ===')
          console.log('File:', filename, 'Type:', fileType, 'Path:', filePath)

          const processedContent = await this.fileProcessor.processFile(filePath, fileType)

          console.log('=== DEBUG: Processing result ===')
          console.log('Has text:', !!processedContent.text)
          console.log('Text length:', processedContent.text?.length || 0)
          console.log('Has error:', !!processedContent.error)
          console.log('Error:', processedContent.error)
          console.log('Metadata:', processedContent.metadata)

          if (processedContent.text && !processedContent.error) {
            // Update the file record with extracted content
            this.dbManager.updateFile(fileId, {
              extracted_content: processedContent.text,
              metadata: JSON.stringify({
                ...JSON.parse(fileRecord.metadata || '{}'),
                processed: true,
                processingMetadata: processedContent.metadata
              })
            })
            console.log('File content processed successfully:', filename)
          } else if (processedContent.error) {
            console.warn('File processing failed:', filename, processedContent.error)
          } else if (!processedContent.text) {
            console.warn('No text content extracted from:', filename)
          }
        } catch (error) {
          console.error('Error processing file content:', filename, error)
        }
      } else if (!processContent) {
        console.log('File indexed (metadata only):', filename)
      } else if (!this.fileProcessor.isFileTypeSupportedSync(fileType)) {
        console.warn('File type not supported for content processing:', fileType)
      }

      return fileId
    } catch (error) {
      console.error('Error indexing file:', filePath, error)
      return null
    }
  }

  // Index all files in the Chatlo folder
  async indexAllFiles(): Promise<void> {
    console.log('Starting file indexing...')
    try {
      await this.indexDirectory(this.chatloFolderPath)
      console.log('File indexing completed')
    } catch (error) {
      console.error('Error during file indexing:', error)
    }
  }

  // Recursively index files in a directory
  private async indexDirectory(dirPath: string): Promise<void> {
    const items = fs.readdirSync(dirPath)
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item)
      const stats = fs.statSync(itemPath)
      
      if (stats.isDirectory()) {
        // Skip hidden directories and node_modules
        if (!item.startsWith('.') && item !== 'node_modules') {
          await this.indexDirectory(itemPath)
        }
      } else if (stats.isFile()) {
        // Skip hidden files and system files
        if (!item.startsWith('.') && !item.startsWith('~')) {
          await this.indexFile(itemPath)
        }
      }
    }
  }

  // Get all indexed files
  getIndexedFiles(): FileRecord[] {
    return this.dbManager.getFiles()
  }

  // Search files by name or content with limit
  searchFiles(query: string, limit: number = 10): FileRecord[] {
    return this.dbManager.searchFiles(query, limit)
  }

  // Process file content on-demand (for chat usage)
  async processFileContent(fileId: string): Promise<boolean> {
    try {
      const file = this.dbManager.getFile(fileId)
      if (!file) {
        console.error('File not found:', fileId)
        return false
      }

      // Skip if already processed
      if (file.extracted_content) {
        console.log('File content already processed:', file.filename)
        return true
      }

      // Check if file still exists
      if (!fs.existsSync(file.filepath)) {
        console.warn('File no longer exists:', file.filepath)
        return false
      }

      const fileType = this.getFileType(file.filename)
      if (!this.fileProcessor.isFileTypeSupportedSync(fileType)) {
        console.log('File type not supported for content processing:', fileType)
        return false
      }

      console.log('Processing file content on-demand:', file.filename)
      const processedContent = await this.fileProcessor.processFile(file.filepath, fileType)

      if (processedContent.text && !processedContent.error) {
        // Update the file record with extracted content
        this.dbManager.updateFile(fileId, {
          extracted_content: processedContent.text,
          metadata: JSON.stringify({
            ...JSON.parse(file.metadata || '{}'),
            processed: true,
            processingMetadata: processedContent.metadata
          })
        })
        console.log('File content processed successfully:', file.filename)
        return true
      } else if (processedContent.error) {
        console.warn('File processing failed:', file.filename, processedContent.error)
        return false
      }

      return false
    } catch (error) {
      console.error('Error processing file content on-demand:', error)
      return false
    }
  }

  // Copy file to Chatlo folder
  async copyFileToUploads(sourcePath: string, filename?: string): Promise<string> {
    // Redirect legacy "Uploads" behavior to shared-dropbox per V02
    const uploadsDir = PathResolver.normalizePath(path.join(this.chatloFolderPath, 'shared-dropbox'))
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true })
    }
    const targetFilename = filename || path.basename(sourcePath)
    const targetPath = PathResolver.normalizePath(path.join(uploadsDir, targetFilename))

    // Ensure unique filename if file already exists
    let finalPath = targetPath
    let counter = 1
    while (fs.existsSync(finalPath)) {
      const ext = path.extname(targetFilename)
      const name = path.basename(targetFilename, ext)
      finalPath = path.join(uploadsDir, `${name}_${counter}${ext}`)
      counter++
    }

    fs.copyFileSync(sourcePath, finalPath)
    await this.indexFile(finalPath)
    return finalPath
  }

  // Save content as file in Chatlo folder
  async saveContentToVault(content: string, filename: string, subfolder: string = 'shared-dropbox'): Promise<string> {
    // Default to shared-dropbox; ensure directory exists
    const targetDir = PathResolver.normalizePath(path.join(this.chatloFolderPath, subfolder))
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true })
    }
    const targetPath = PathResolver.normalizePath(path.join(targetDir, filename))

    // Ensure unique filename if file already exists
    let finalPath = targetPath
    let counter = 1
    while (fs.existsSync(finalPath)) {
      const ext = path.extname(filename)
      const name = path.basename(filename, ext)
      finalPath = path.join(targetDir, `${name}_${counter}${ext}`)
      counter++
    }

    // Check if content is base64 (for images) or plain text
    const isBase64 = this.isBase64String(content)

    if (isBase64) {
      // Convert base64 to binary buffer for images
      const buffer = Buffer.from(content, 'base64')
      fs.writeFileSync(finalPath, buffer)
    } else {
      // Save as UTF-8 text for regular files
      fs.writeFileSync(finalPath, content, 'utf8')
    }

    await this.indexFile(finalPath)
    return finalPath
  }

  /**
   * @deprecated Use saveContentToVault(content, filename, subfolder)
   */
  async saveContentAsFile(content: string, filename: string, subfolder: string = 'shared-dropbox'): Promise<string> {
    console.warn('[DEPRECATED] saveContentAsFile() is deprecated. Use saveContentToVault().')
    return this.saveContentToVault(content, filename, subfolder)
  }

  // Helper method to detect base64 strings
  private isBase64String(str: string): boolean {
    try {
      // Check if string looks like base64 and can be decoded
      return Buffer.from(str, 'base64').toString('base64') === str
    } catch {
      return false
    }
  }

  // Delete file and remove from index
  async deleteFile(fileId: string): Promise<boolean> {
    try {
      const file = this.dbManager.getFile(fileId)
      if (!file) return false

      // Delete physical file if it exists
      if (fs.existsSync(file.filepath)) {
        fs.unlinkSync(file.filepath)
      }

      // Remove from database
      this.dbManager.deleteFile(fileId)
      console.log('Deleted file:', file.filename)
      return true
    } catch (error) {
      console.error('Error deleting file:', error)
      return false
    }
  }

  // Get file content
  getFileContent(filePath: string): string | null {
    try {
      if (fs.existsSync(filePath)) {
        const buffer = fs.readFileSync(filePath)
        // Return as base64 string for easier handling in renderer
        return buffer.toString('base64')
      }
      return null
    } catch (error) {
      console.error('Error reading file:', error)
      return null
    }
  }

  // Check if file exists
  fileExists(filePath: string): boolean {
    return fs.existsSync(filePath)
  }
}
