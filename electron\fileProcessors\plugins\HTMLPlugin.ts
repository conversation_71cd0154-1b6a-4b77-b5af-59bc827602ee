/**
 * HTML File Processing Plugin
 * Core plugin for processing HTML files without external dependencies
 */

import * as fs from 'fs'
import * as path from 'path'
import { FileProcessorPlugin, ProcessedFileContent } from '../types'

export default class HTMLPlugin implements FileProcessorPlugin {
  name = 'HTMLPlugin'
  version = '1.0.0'
  description = 'Core plugin for processing HTML files'
  author = 'ChatLo Team'
  optional = false

  supportedTypes = ['html']
  supportedExtensions = ['.html', '.htm']

  canProcess(filePath: string, fileType: string): boolean {
    const extension = path.extname(filePath).toLowerCase()
    return this.supportedTypes.includes(fileType) || this.supportedExtensions.includes(extension)
  }

  async process(filePath: string): Promise<ProcessedFileContent> {
    try {
      const stats = await fs.promises.stat(filePath)
      const content = await fs.promises.readFile(filePath, 'utf8')

      const text = this.extractVisibleText(content)
      const metadata = this.extractMetadata(content)

      return {
        text,
        metadata: {
          ...metadata,
          fileSize: stats.size,
          lastModified: stats.mtime,
          extension: path.extname(filePath),
          processor: this.name
        }
      }
    } catch (error: any) {
      return {
        error: `Failed to process HTML file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  private extractVisibleText(html: string): string {
    // Remove scripts/styles
    const withoutScripts = html
      .replace(/<script[\s\S]*?<\/script>/gi, '')
      .replace(/<style[\s\S]*?<\/style>/gi, '')

    // Get body content
    const bodyMatch = withoutScripts.match(/<body[^>]*>([\s\S]*?)<\/body>/i)
    const body = bodyMatch ? bodyMatch[1] : withoutScripts

    // Replace block elements with newlines for readability
    const withBreaks = body
      .replace(/<\/(p|div|section|article|header|footer|li|ul|ol|table|tr|h[1-6])>/gi, '$&\n')

    // Strip tags
    const text = withBreaks.replace(/<[^>]+>/g, '')

    // Decode basic entities
    const decoded = text
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")

    return decoded.split('\n').map(l => l.trim()).filter(Boolean).join('\n')
  }

  private extractMetadata(html: string) {
    const titleMatch = html.match(/<title[^>]*>([\s\S]*?)<\/title>/i)
    const title = titleMatch ? titleMatch[1].trim() : ''

    const metaTags = Array.from(html.matchAll(/<meta\s+([^>]*?)>/gi)).map(m => m[1])
    const meta: Record<string, string> = {}
    for (const attrs of metaTags) {
      const nameMatch = attrs.match(/(?:name|property)=["']([^"']+)["']/i)
      const contentMatch = attrs.match(/content=["']([^"']+)["']/i)
      if (nameMatch && contentMatch) {
        meta[nameMatch[1]] = contentMatch[1]
      }
    }

    const linkCount = (html.match(/<a\s+[^>]*href=/gi) || []).length
    const imageCount = (html.match(/<img\s+[^>]*src=/gi) || []).length
    const codeBlocks = (html.match(/<pre[\s\S]*?<\/pre>/gi) || []).length

    const words = this.extractVisibleText(html).split(/\s+/).filter(w => w.length > 0)

    return {
      title,
      meta,
      elements: {
        links: linkCount,
        images: imageCount,
        codeBlocks
      },
      words: words.length,
      lines: words.join(' ').split('\n').length
    }
  }
}


