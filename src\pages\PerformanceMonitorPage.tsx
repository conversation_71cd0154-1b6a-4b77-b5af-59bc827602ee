/**
 * Performance Monitor Page
 * Shows file processing reports with pagination
 * First page: File processing reports
 * Second page: Original performance data
 */

import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from '../components/Icons/index'
import { fileProcessingQueue, FileProcessingTask, QueueStats } from '../services/fileProcessingQueue'
import { performanceMonitor } from '../services/performanceMonitor'

interface PerformanceMonitorPageProps {
  onClose: () => void
}

const PerformanceMonitorPage: React.FC<PerformanceMonitorPageProps> = ({ onClose }) => {
  const [currentPage, setCurrentPage] = useState<'processing' | 'system'>('processing')
  const [stats, setStats] = useState<QueueStats | null>(null)
  const [tasks, setTasks] = useState<{
    pending: FileProcessingTask[]
    processing: FileProcessingTask[]
    completed: FileProcessingTask[]
    failed: FileProcessingTask[]
  } | null>(null)
  const [selectedTab, setSelectedTab] = useState<'all' | 'completed' | 'failed' | 'processing'>('all')
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null)

  useEffect(() => {
    loadData()
    
    // Auto-refresh every 2 seconds
    const interval = setInterval(loadData, 2000)
    setRefreshInterval(interval)

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [])

  const loadData = async () => {
    try {
      const queueStats = fileProcessingQueue.getStats()
      const allTasks = fileProcessingQueue.getAllTasks()
      
      setStats(queueStats)
      setTasks(allTasks)
    } catch (error) {
      console.error('Failed to load performance data:', error)
    }
  }

  const handleClearHistory = () => {
    fileProcessingQueue.clearHistory()
    loadData()
  }

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
    return `${(ms / 60000).toFixed(1)}m`
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes}B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return ICONS.checkCircle
      case 'failed': return ICONS.exclamationTriangle
      case 'processing': return ICONS.spinner
      default: return ICONS.clock
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400'
      case 'failed': return 'text-red-400'
      case 'processing': return 'text-blue-400'
      default: return 'text-gray-400'
    }
  }

  const getFilteredTasks = (): FileProcessingTask[] => {
    if (!tasks) return []
    
    switch (selectedTab) {
      case 'completed': return tasks.completed
      case 'failed': return tasks.failed
      case 'processing': return tasks.processing
      default: return [
        ...tasks.pending,
        ...tasks.processing,
        ...tasks.completed,
        ...tasks.failed
      ]
    }
  }

  const renderProcessingReport = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <FontAwesomeIcon icon={ICONS.chartLine} className="text-2xl text-primary" />
          <div>
            <h2 className="text-xl font-semibold text-supplement1">File Processing Report</h2>
            <p className="text-sm text-gray-400">Real-time monitoring of file processing tasks</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={loadData}
            className="flex items-center gap-2 px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
          >
            <FontAwesomeIcon icon={ICONS.refresh} className="text-sm" />
            Refresh
          </button>
          
          <button
            onClick={handleClearHistory}
            className="flex items-center gap-2 px-3 py-2 bg-red-600 hover:bg-red-500 rounded-lg transition-colors"
          >
            <FontAwesomeIcon icon={ICONS.trash} className="text-sm" />
            Clear History
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
            <div className="flex items-center gap-3">
              <FontAwesomeIcon icon={ICONS.clock} className="text-blue-400 text-xl" />
              <div>
                <div className="text-2xl font-bold text-supplement1">{stats.pending}</div>
                <div className="text-sm text-gray-400">Pending</div>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
            <div className="flex items-center gap-3">
              <FontAwesomeIcon icon={ICONS.spinner} className="text-blue-400 text-xl" />
              <div>
                <div className="text-2xl font-bold text-supplement1">{stats.processing}</div>
                <div className="text-sm text-gray-400">Processing</div>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
            <div className="flex items-center gap-3">
              <FontAwesomeIcon icon={ICONS.checkCircle} className="text-green-400 text-xl" />
              <div>
                <div className="text-2xl font-bold text-supplement1">{stats.completed}</div>
                <div className="text-sm text-gray-400">Completed</div>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
            <div className="flex items-center gap-3">
              <FontAwesomeIcon icon={ICONS.exclamationTriangle} className="text-red-400 text-xl" />
              <div>
                <div className="text-2xl font-bold text-supplement1">{stats.failed}</div>
                <div className="text-sm text-gray-400">Failed</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Task Tabs */}
      <div className="bg-gray-800 rounded-lg border border-gray-700">
        <div className="flex border-b border-gray-700">
          {['all', 'completed', 'failed', 'processing'].map((tab) => (
            <button
              key={tab}
              onClick={() => setSelectedTab(tab as any)}
              className={`px-4 py-3 text-sm font-medium transition-colors ${
                selectedTab === tab
                  ? 'text-primary border-b-2 border-primary'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>

        {/* Task List */}
        <div className="p-4">
          {tasks && getFilteredTasks().length > 0 ? (
            <div className="space-y-3">
              {getFilteredTasks().map((task, index) => (
                <div
                  key={`${task.id}-${index}`}
                  className="flex items-center gap-4 p-3 bg-gray-700 rounded-lg border border-gray-600"
                >
                  <FontAwesomeIcon
                    icon={getStatusIcon(task.status)}
                    className={`text-lg ${getStatusColor(task.status)}`}
                  />
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <FontAwesomeIcon icon={ICONS.file} className="text-gray-400 text-sm" />
                      <span className="font-medium text-supplement1 truncate">{task.fileName}</span>
                    </div>
                    <div className="text-sm text-gray-400">
                      {task.status === 'completed' && `Completed in ${formatDuration(task.duration || 0)}`}
                      {task.status === 'failed' && `Failed: ${task.error || 'Unknown error'}`}
                      {task.status === 'processing' && 'Processing...'}
                      {task.status === 'pending' && 'Queued for processing'}
                    </div>
                  </div>
                  
                  <div className="text-right text-sm text-gray-400">
                    <div>{formatFileSize(task.fileSize || 0)}</div>
                    <div>{new Date(task.createdAt).toLocaleTimeString()}</div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-400">
              <FontAwesomeIcon icon={ICONS.file} className="text-4xl mb-4 opacity-50" />
              <p>No tasks found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )

  const renderSystemPerformance = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <FontAwesomeIcon icon={ICONS.chartLine} className="text-2xl text-primary" />
        <div>
          <h2 className="text-xl font-semibold text-supplement1">System Performance</h2>
          <p className="text-sm text-gray-400">Real-time system metrics and performance data</p>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
          <h3 className="text-lg font-semibold text-supplement1 mb-4">Memory Usage</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Used</span>
              <span className="text-supplement1">2.1 GB</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Available</span>
              <span className="text-supplement1">5.9 GB</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div className="bg-primary h-2 rounded-full" style={{ width: '26%' }}></div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
          <h3 className="text-lg font-semibold text-supplement1 mb-4">CPU Usage</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Current</span>
              <span className="text-supplement1">23%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Peak</span>
              <span className="text-supplement1">67%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div className="bg-primary h-2 rounded-full" style={{ width: '23%' }}></div>
            </div>
          </div>
        </div>
      </div>

      {/* System Info */}
      <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
        <h3 className="text-lg font-semibold text-supplement1 mb-4">System Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <div className="flex justify-between py-2 border-b border-gray-700">
              <span className="text-gray-400">Platform</span>
              <span className="text-supplement1">Windows 10</span>
            </div>
            <div className="flex justify-between py-2 border-b border-gray-700">
              <span className="text-gray-400">Node.js Version</span>
              <span className="text-supplement1">v18.17.0</span>
            </div>
            <div className="flex justify-between py-2 border-b border-gray-700">
              <span className="text-gray-400">Electron Version</span>
              <span className="text-supplement1">v25.0.0</span>
            </div>
          </div>
          <div>
            <div className="flex justify-between py-2 border-b border-gray-700">
              <span className="text-gray-400">Uptime</span>
              <span className="text-supplement1">2h 34m</span>
            </div>
            <div className="flex justify-between py-2 border-b border-gray-700">
              <span className="text-gray-400">Processes</span>
              <span className="text-supplement1">12</span>
            </div>
            <div className="flex justify-between py-2 border-b border-gray-700">
              <span className="text-gray-400">Threads</span>
              <span className="text-supplement1">48</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-900 w-full max-w-6xl h-[90vh] rounded-lg border border-gray-700 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h1 className="text-2xl font-bold text-supplement1">Performance Monitor</h1>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
          >
            <FontAwesomeIcon icon={ICONS.times} className="text-xl" />
          </button>
        </div>

        {/* Navigation */}
        <div className="flex border-b border-gray-700">
          <button
            onClick={() => setCurrentPage('processing')}
            className={`px-6 py-3 text-sm font-medium transition-colors ${
              currentPage === 'processing'
                ? 'text-primary border-b-2 border-primary'
                : 'text-gray-400 hover:text-gray-300'
            }`}
          >
            <FontAwesomeIcon icon={ICONS.file} className="mr-2" />
            Processing Report
          </button>
          <button
            onClick={() => setCurrentPage('system')}
            className={`px-6 py-3 text-sm font-medium transition-colors ${
              currentPage === 'system'
                ? 'text-primary border-b-2 border-primary'
                : 'text-gray-400 hover:text-gray-300'
            }`}
          >
            <FontAwesomeIcon icon={ICONS.chartLine} className="mr-2" />
            System Performance
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {currentPage === 'processing' ? renderProcessingReport() : renderSystemPerformance()}
        </div>
      </div>
    </div>
  )
}

export default PerformanceMonitorPage
