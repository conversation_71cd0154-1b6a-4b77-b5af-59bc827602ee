@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom utilities */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@layer base {

  body {
    @apply bg-gray-900 text-white font-sans antialiased;
    font-family: 'Inter', system-ui, sans-serif;
  }

  html, body, #root {
    @apply h-full;
  }
}

@layer components {
  /* Glassmorphism effects */
  .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-strong {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  .glass-subtle {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  /* Glow effects */
  .glow-primary {
    box-shadow: 0 0 20px rgba(138, 176, 187, 0.3);
  }

  .glow-secondary {
    box-shadow: 0 0 15px rgba(255, 131, 131, 0.2);
  }

  /* Legacy chat bubble classes - keeping for backward compatibility */
  .chat-bubble {
    @apply rounded-lg px-4 py-2 text-sm max-w-md;
  }

  /* Hide scrollbars for artifact tabs */
  .artifact-tabs-scroll {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .artifact-tabs-scroll::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  .chat-bubble-user {
    @apply bg-primary text-gray-900;
  }

  .chat-bubble-assistant {
    @apply bg-gray-800 text-supplement1;
  }

  .sidebar-item {
    @apply flex gap-3 items-center w-full px-6 py-3 text-sm font-medium hover:bg-gray-800 focus:bg-gray-800 transition-colors;
  }

  .input-field {
    @apply bg-gray-900 border border-gray-700 rounded-lg px-4 py-3 text-sm placeholder-gray-500 focus:ring-2 focus:ring-primary focus:border-transparent outline-none;
  }

  .btn-primary {
    @apply bg-primary hover:bg-primary/80 text-gray-900 font-medium px-4 py-2 rounded-lg transition-colors;
  }

  .btn-secondary {
    @apply bg-gray-800 hover:bg-gray-700 text-supplement1 font-medium px-4 py-2 rounded-lg transition-colors;
  }

  .btn-ghost {
    @apply hover:bg-gray-800 text-supplement1 p-2 rounded-lg transition-colors;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .context-card {
    transition: all 0.2s ease;
  }

  .context-card:hover {
    transform: translateY(-1px);
  }

  /* ChatLo Design System v1 Components */

  /* Markdown Preview - Obsidian-like */
  .markdown-body {
    @apply text-supplement1 leading-7;
  }

  .markdown-body h1 {
    @apply text-3xl font-extrabold text-white mt-12 mb-4 tracking-tight;
  }

  .markdown-body h2 {
    @apply text-2xl font-bold text-white mt-10 mb-3 tracking-tight;
  }

  .markdown-body h3 {
    @apply text-xl font-semibold text-white mt-8 mb-2.5 tracking-tight;
  }

  .markdown-body h4 {
    @apply text-lg font-semibold text-white mt-6 mb-2 tracking-tight;
  }

  .markdown-body h5 {
    @apply text-base font-semibold text-white mt-5 mb-2 tracking-tight;
  }

  .markdown-body h6 {
    @apply text-sm font-semibold text-white mt-4 mb-2 tracking-tight;
  }

  .markdown-body p {
    @apply text-[15px] text-gray-200 mb-3;
  }

  .markdown-body ul {
    @apply list-disc pl-6 space-y-1.5 my-3 text-gray-200;
  }

  .markdown-body ol {
    @apply list-decimal pl-6 space-y-1.5 my-3 text-gray-200;
  }

  .markdown-body li {
    @apply text-[15px];
  }

  .markdown-body code {
    @apply bg-gray-800 text-gray-100 px-1.5 py-0.5 rounded font-mono text-sm;
  }

  .markdown-body pre {
    @apply bg-gray-800/80 border border-gray-700 rounded-lg p-4 my-4 overflow-x-auto shadow-sm;
  }

  .markdown-body blockquote {
    @apply border-l-4 border-primary/40 pl-4 text-gray-300 italic my-4;
  }

  .markdown-body table {
    @apply w-full text-sm border-separate border-spacing-0 my-4;
  }

  .markdown-body th {
    @apply bg-gray-800 text-gray-200 font-semibold p-2 border-b border-gray-700;
  }

  .markdown-body td {
    @apply p-2 border-b border-gray-700 text-gray-300;
  }

  /* Buttons */
  .u1-button-primary {
    @apply bg-primary hover:bg-primary/80 text-gray-900 font-medium py-3 px-6 rounded-lg transition-colors;
  }

  .u1-button-secondary {
    @apply bg-secondary hover:bg-secondary/80 text-white font-medium py-3 px-6 rounded-lg transition-colors;
  }

  .u1-button-outline {
    @apply border border-primary text-primary hover:bg-primary hover:text-gray-900 font-medium py-3 px-6 rounded-lg transition-colors;
  }

  .u1-button-icon {
    @apply w-10 h-10 bg-primary hover:bg-primary/80 text-gray-900 rounded-lg transition-colors flex items-center justify-center;
  }

  .u1-button-ghost {
    @apply hover:bg-gray-700 text-supplement1 p-2 rounded-lg transition-colors;
  }

  .u1-button-nav {
    @apply w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1;
  }

  /* Input Fields */
  .u1-input-field {
    @apply bg-gray-900 border border-gray-700 rounded-lg px-4 py-3 text-sm placeholder-gray-500 focus:ring-2 focus:ring-primary focus:border-transparent outline-none;
  }

  .u1-input-search {
    @apply bg-gray-900 border border-gray-700 rounded-lg px-4 py-3 text-sm placeholder-gray-500 focus:ring-2 focus:ring-primary focus:border-transparent outline-none;
  }

  .u1-textarea {
    @apply bg-gray-900 border border-gray-700 rounded-lg px-4 py-3 text-sm placeholder-gray-500 focus:ring-2 focus:ring-primary focus:border-transparent outline-none resize-none;
  }

  .u1-chat-input {
    @apply flex gap-3 items-center;
  }

  /* Cards & Containers */
  .u1-card {
    @apply bg-gray-800 rounded-lg p-6;
  }

  .u1-chat-bubble-user {
    @apply bg-primary rounded-2xl rounded-tr-md p-4 max-w-lg text-gray-900;
  }

  .u1-chat-bubble-assistant {
    @apply bg-gray-800 rounded-2xl rounded-tl-md p-4 max-w-lg text-supplement1;
  }

  .u1-sidebar-item {
    @apply flex gap-3 items-center w-full px-6 py-3 text-sm font-medium hover:bg-gray-700 focus:bg-gray-700 transition-colors rounded-lg cursor-pointer;
  }

  .u1-status-card {
    @apply bg-gray-700/50 rounded-lg p-3;
  }

  .u1-artifact-card {
    @apply bg-gray-700/50 rounded-lg p-4;
  }

  /* Navigation */
  .u1-nav-iconbar {
    @apply w-12 bg-gray-900 flex flex-col items-center py-2;
  }

  .u1-nav-icon {
    @apply w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1;
  }

  .u1-nav-tabs {
    @apply flex gap-2;
  }

  .u1-tab-active {
    @apply bg-primary/20 text-primary border border-primary/30 px-3 py-2 rounded-t-lg text-sm font-medium;
  }

  .u1-tab-inactive {
    @apply text-gray-400 hover:text-supplement1 px-3 py-2 rounded-t-lg text-sm font-medium transition-colors;
  }

  /* Badges & Labels */
  .u1-badge-primary {
    @apply bg-primary/20 text-primary border border-primary/30 px-2 py-1 rounded-full text-xs font-medium;
  }

  .u1-badge-secondary {
    @apply bg-secondary/20 text-secondary border border-secondary/30 px-2 py-1 rounded-full text-xs font-medium;
  }

  .u1-badge-success {
    @apply bg-green-500/20 text-green-400 border border-green-500/30 px-2 py-1 rounded-full text-xs font-medium;
  }

  .u1-badge-warning {
    @apply bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 px-2 py-1 rounded-full text-xs font-medium;
  }

  .u1-badge-error {
    @apply bg-red-500/20 text-red-400 border border-red-500/30 px-2 py-1 rounded-full text-xs font-medium;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #525252;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #737373;
}

/* Selection */
::selection {
  background: rgba(99, 102, 241, 0.6);
}
