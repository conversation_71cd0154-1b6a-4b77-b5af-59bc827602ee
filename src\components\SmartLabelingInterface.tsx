/**
 * Smart Labeling Interface Component
 * Interactive label selection interface with AI-powered key ideas extraction
 */

import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import {
  KeyIdea,
  LabelSelectionState,
  ProcessingResult,
  FileIntelligence
} from '../types/fileIntelligenceTypes'
import { localModelService } from '../services/localModelService'
import { intelligence as intelligenceClient } from '../api/UnifiedAPIClient'

interface SmartLabelingInterfaceProps {
  filePath: string
  fileContent: string
  onLabelsChanged?: (selectedLabels: KeyIdea[]) => void
  onProcessingComplete?: (result: ProcessingResult) => void
}

export const SmartLabelingInterface: React.FC<SmartLabelingInterfaceProps> = ({
  filePath,
  fileContent,
  onLabelsChanged,
  onProcessingComplete
}) => {
  console.log('[LABELS] 🚀 SmartLabelingInterface: Component mounted/re-rendered with:', {
    filePath,
    fileContentLength: fileContent?.length || 0,
    hasOnLabelsChanged: !!onLabelsChanged,
    hasOnProcessingComplete: !!onProcessingComplete
  })

  const [labelState, setLabelState] = useState<LabelSelectionState>({
    available_ideas: [],
    selected_idea_ids: [],
    user_annotation: '',
    processing_status: 'idle',
    show_all_ideas: false
  })

  const [isProcessing, setIsProcessing] = useState(false)
  const [processingProgress, setProcessingProgress] = useState(0)
  const [showUserInput, setShowUserInput] = useState(false)
  const [isOrganizing, setIsOrganizing] = useState(false)
  const [lastSessionSignature, setLastSessionSignature] = useState<string | null>(null)

  // Process file content when component mounts or file changes
  useEffect(() => {
    if (filePath && fileContent) {
      console.log('[LABELS] 🔄 SmartLabelingInterface: useEffect triggered')
      console.log('[LABELS] 🔄 SmartLabelingInterface: filePath:', filePath)
      console.log('[LABELS] 🔄 SmartLabelingInterface: fileContent length:', fileContent.length)
      console.log('[LABELS] 🔄 SmartLabelingInterface: onLabelsChanged callback:', !!onLabelsChanged)
      console.log('[LABELS] 🔄 SmartLabelingInterface: Starting intelligence loading process (persisted first)')
      
      // Check kernel status first
      checkAndDisplayKernelStatus()

      // Load or process intelligence (will handle corruption internally if needed)
      loadOrProcessFileIntelligence()
    }
  }, [filePath, fileContent])

  // Subscribe to intelligence updates for this file to auto-refresh persisted data
  useEffect(() => {
    let unsubscribeRenderer: (() => void) | null = null
    let subscriptionId: string | null = null

    const setup = async () => {
      try {
        const sub = await window.electronAPI.invoke('events:subscribe', 'intelligence', { eventNames: ['intelligence:updated'] })
        subscriptionId = sub?.subscriptionId
        if (!subscriptionId) return
        const channel = `events:subscription:${subscriptionId}`
        const handler = (_payload: any) => {
          const { payload } = _payload || {}
          const updatedPath: string | undefined = payload?.filePath
          if (!updatedPath || !filePath) return
          const norm = (p: string) => p.replace(/\\/g, '/').toLowerCase()
          if (norm(updatedPath) === norm(filePath)) {
            // Re-load persisted data
            loadOrProcessFileIntelligence()
          }
        }
        const remove = (window as any).electronAPI?.events?.on
          ? (window as any).electronAPI.events.on(channel, handler)
          : (() => {
              try {
                const { ipcRenderer } = require('electron')
                ipcRenderer.on(channel, (_: any, data: any) => handler(data))
                return () => ipcRenderer.removeAllListeners(channel)
              } catch {
                return () => {}
              }
            })()
        unsubscribeRenderer = remove
      } catch (e) {
        console.warn('[LABELS] Failed to subscribe to intelligence updates', e)
      }
    }

    setup()

    return () => {
      try { unsubscribeRenderer && unsubscribeRenderer() } catch {}
      if (subscriptionId) {
        window.electronAPI.invoke('events:unsubscribe', subscriptionId).catch(() => {})
      }
    }
  }, [filePath])

  /**
   * Clear any existing corrupted intelligence data
   */
  const clearCorruptedIntelligence = async () => {
    if (!filePath || !fileContent) return
    
    try {
      console.log('[LABELS] 🧹 Clearing any corrupted intelligence data for:', filePath)
      
      // This will trigger a fresh analysis if no valid intelligence is found
      setLabelState(prev => ({
        ...prev,
        available_ideas: [],
        selected_idea_ids: [],
        processing_status: 'idle',
        error_message: undefined
      }))
      
      console.log('[LABELS] 🧹 Intelligence state cleared, ready for fresh analysis')
    } catch (error) {
      console.log('[LABELS] 🧹 Error clearing intelligence state:', error)
    }
  }

  /**
   * Check kernel status and display it
   */
  const checkAndDisplayKernelStatus = async () => {
    try {
      console.log('[LABELS] 🔍 KERNEL-STATUS: Checking kernel availability')
      
      // Check if we can invoke intelligence methods
      const hasIntelligence = await checkKernelIntelligenceStatus(filePath)
      const hasAnalyze = true // We'll check this when we try to use it
      const hasGet = true // We'll check this when we try to use it
      
      const kernelStatus = {
        available: !!window.electronAPI?.invoke,
        hasAnalyze,
        hasGet,
        hasIntelligence,
        electronAPI: !!window.electronAPI,
        intelligenceAPI: !!window.electronAPI?.invoke
      }
      
      console.log('[LABELS] 🔍 KERNEL-STATUS: Kernel status:', kernelStatus)
      
      // Display kernel status in the UI
      if (!kernelStatus.available) {
        setLabelState(prev => ({
          ...prev,
          error_message: 'Kernel intelligence API not available. Please ensure the application is properly initialized.'
        }))
      } else if (!kernelStatus.hasIntelligence) {
        console.log('[LABELS] 🔍 KERNEL-STATUS: Kernel available but no existing intelligence found')
      } else {
        console.log('[LABELS] 🔍 KERNEL-STATUS: Kernel available and has existing intelligence')
      }
      
      return kernelStatus
    } catch (error) {
      console.log('[LABELS] 🔍 KERNEL-STATUS: Error checking kernel status:', error)
      return null
    }
  }

  /**
   * Check if kernel has intelligence data for this file (no path resolution needed)
   */
  const checkKernelIntelligenceStatus = async (filePath: string): Promise<boolean> => {
    try {
      console.log('[LABELS] 🔍 KERNEL-STATUS: Checking if kernel has intelligence for file')
      console.log('[LABELS] 🔍 KERNEL-STATUS: filePath:', filePath)
      
      // Try to query kernel directly using invoke
      if (window.electronAPI?.invoke) {
        // Use a dummy vaultPath since kernel should handle file identification
        const dummyVaultPath = 'kernel-query'
        
        try {
          const result = await window.electronAPI.invoke('intelligence:read', filePath, dummyVaultPath)
          const hasIntelligence = !!(
            result && result.success && Array.isArray(result.data?.key_ideas) && result.data.key_ideas.length > 0
          )
          
          console.log('[LABELS] 🔍 KERNEL-STATUS: Kernel response:', {
            success: result?.success,
            hasData: !!result?.data,
            ideasCount: Array.isArray(result?.data?.key_ideas) ? result.data.key_ideas.length : 0,
            hasIntelligence
          })
          
          return hasIntelligence
        } catch (error) {
          console.log('[LABELS] 🔍 KERNEL-STATUS: Kernel query failed:', error)
          return false
        }
      } else {
        console.log('[LABELS] 🔍 KERNEL-STATUS: ❌ Kernel intelligence API not available')
      }
      
      return false
    } catch (error) {
      console.log('[LABELS] 🔍 KERNEL-STATUS: Error checking kernel status:', error)
      return false
    }
  }

  /**
   * V02 COMPLIANT: Persist analysis results using unified client
   */
  const persistAnalysisResults = async (keyIdeas: KeyIdea[], processingTime: number, modelUsed: string): Promise<void> => {
    try {
      if (!filePath || !fileContent) {
        console.log('[LABELS] ⚠️ Cannot persist - missing filePath or fileContent')
        return
      }

      const { extractContextPath } = await import('../utils/vaultPath')
      const contextPath = extractContextPath(filePath)

      if (!contextPath) {
        console.log('[LABELS] ⚠️ Cannot persist - could not extract context path from:', filePath)
        return
      }

      // V02 UNIFIED: Create intelligence-format data for single file storage
      const intelligenceData: FileIntelligence = {
        file_path: filePath,
        key_ideas: keyIdeas,
        weighted_entities: [],
        human_connections: [],
        processing_confidence: keyIdeas.length > 0 ? 0.85 : 0,
        analysis_metadata: {
          processing_time_ms: processingTime,
          model_used: modelUsed,
          timestamp: new Date().toISOString()
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        smart_annotations: []
      }

      console.log('[LABELS] 💾 Persisting analysis results:', {
        filePath,
        contextPath,
        ideasCount: keyIdeas.length
      })

      // V02 UNIFIED: Write intelligence data to single file location
      const result = await intelligenceClient.write(filePath, contextPath, { json: intelligenceData })

      if (result.success) {
        console.log('[LABELS] ✅ Analysis results persisted successfully')
      } else {
        console.log('[LABELS] ❌ Failed to persist analysis results:', result.error)
      }
    } catch (error) {
      console.log('[LABELS] ❌ Error persisting analysis results:', error)
    }
  }

  /**
   * V02 COMPLIANT: Query kernel for existing intelligence using unified client
   */
  const queryKernelForIntelligence = async (filePath: string): Promise<FileIntelligence | null> => {
    try {
      console.log('[LABELS] 🔍 KERNEL: Querying for existing intelligence:', filePath)

      // Extract proper context path from file path
      const { extractContextPath } = await import('../utils/vaultPath')
      const contextPath = extractContextPath(filePath)

      if (!contextPath) {
        console.log('[LABELS] 🔍 KERNEL: ❌ Could not extract context path from:', filePath)
        return null
      }

      console.log('[LABELS] 🔍 KERNEL: Using contextPath:', contextPath)

      // V02 COMPLIANT: Use unified client for all intelligence operations
      const result = await intelligenceClient.read(filePath, contextPath)

      if (result && result.success && result.data) {
        console.log('[LABELS] 🔍 KERNEL: ✅ Kernel returned intelligence with', result.data.key_ideas?.length || 0, 'ideas')
        return result.data
      } else {
        console.log('[LABELS] 🔍 KERNEL: ℹ️ Kernel returned no intelligence:', result?.error || 'no data')
      }

      return null
    } catch (error) {
      console.log('[LABELS] 🔍 KERNEL: Error in intelligence query:', error)
      return null
    }
  }

  /**
   * Load existing intelligence or process file content
   */
  const loadOrProcessFileIntelligence = async () => {
    if (!filePath) {
      console.log('[LABELS] ❌ SmartLabelingInterface: loadOrProcessFileIntelligence called but missing filePath')
      return
    }

    console.log('[LABELS] 🔄 SmartLabelingInterface: loadOrProcessFileIntelligence called', {
      filePath,
      fileContentLength: fileContent?.length || 0
    })

    try {
      // KERNEL-ONLY: Query kernel directly for existing intelligence
      console.log('[LABELS] 🔄 KERNEL: Querying kernel for existing intelligence')
      const kernelIntelligence = await queryKernelForIntelligence(filePath)
      
      if (kernelIntelligence) {
        console.log('[LABELS] ✅ KERNEL: Found existing intelligence with', kernelIntelligence.key_ideas.length, 'ideas')
        
        // Use kernel intelligence data directly
        setLabelState(prev => ({
          ...prev,
          available_ideas: kernelIntelligence.key_ideas,
          selected_idea_ids: kernelIntelligence.key_ideas
            .filter((idea: any) => idea.auto_selected)
            .map((idea: any) => idea.id),
          processing_status: 'complete'
        }))

        // Notify parent components
        setTimeout(() => {
          onLabelsChanged?.(kernelIntelligence.key_ideas)
        }, 0)

        console.log('[FILE-INTEL] Loaded existing intelligence from kernel:', {
          filePath,
          ideasCount: kernelIntelligence.key_ideas.length,
          entitiesCount: kernelIntelligence.weighted_entities.length
        })
        return
      }

      // No existing intelligence found — do NOT auto-process (respect JIT rule)
      console.log('[LABELS] 🔄 SmartLabelingInterface: Setting idle state - no existing intelligence')
      setLabelState(prev => ({ ...prev, processing_status: 'idle', available_ideas: [], selected_idea_ids: [] }))

      // IMPORTANT: Notify overlay even when no intelligence exists
      console.log('[LABELS] 🏷️ SmartLabelingInterface: Notifying overlay with empty ideas array (no existing intelligence)')
      setTimeout(() => {
        console.log('[LABELS] 🏷️ Calling onLabelsChanged with empty array')
        onLabelsChanged?.([])
        console.log('[LABELS] 🏷️ onLabelsChanged with empty array completed')
      }, 0)

      if (!fileContent) {
        console.log('[LABELS] ℹ️ SmartLabelingInterface: No stored intelligence and no fileContent available. Waiting for explicit Analyze action...')
      } else {
        console.log('[LABELS] ℹ️ SmartLabelingInterface: No stored intelligence found. Waiting for user action to analyze...')
      }
    } catch (error) {
      console.warn('[LABELS] ❌ SmartLabelingInterface: Failed to load existing intelligence:', error)
    }
  }

  /**
   * Compute a simple stable hash for duplicate-guard
   */
  const computeSignature = (ideas: KeyIdea[]): string => {
    // Deterministic signature by sorting IDs then hashing string
    const data = ideas
      .map(i => ({ id: i.id, text: i.text, rel: i.relevance_score, u: i.user_confirmed, a: i.auto_selected }))
      .sort((a, b) => a.id.localeCompare(b.id))
    const str = JSON.stringify(data)
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const c = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + c
      hash |= 0
    }
    return Math.abs(hash).toString(16)
  }

  // Persist current ideas as a session, debounced and duplicate-safe
  const persistSession = async () => {
    if (isOrganizing) return
    if (!filePath) return
    
    // Use a dummy vaultPath since kernel should handle file identification
    const dummyVaultPath = 'kernel-session'
    
    const ideas = labelState.available_ideas
    if (!ideas || ideas.length === 0) return

    const signature = computeSignature(ideas)
    if (lastSessionSignature && lastSessionSignature === signature) {
      // Skip duplicate
      return
    }

    try {
      setIsOrganizing(true)
      // Build a minimal session payload
      const session = {
        source: 'smart_labeling',
        key_ideas: ideas,
        summary: ideas[0]?.text || '',
        entities: [],
        created_at: new Date().toISOString()
      }
      await window.electronAPI.invoke('intelligence:writeSession', filePath, dummyVaultPath, session)
      setLastSessionSignature(signature)
    } catch (error) {
      console.warn('[LABELS] Failed to persist session:', error)
    } finally {
      setIsOrganizing(false)
    }
  }

  /**
   * Process file content with AI analysis
   */
  const processFileIntelligence = async () => {
    if (!fileContent || isProcessing) return

    setIsProcessing(true)
    setLabelState(prev => ({ 
      ...prev, 
      processing_status: 'processing',
      error_message: undefined // Clear any previous error
    }))
    setProcessingProgress(0)

    // Declare progressInterval at function scope so it's accessible in catch block
    let progressInterval: NodeJS.Timeout | null = null
    const startTime = Date.now()

    try {
      console.log('[LABELS] 🤖 processFileIntelligence: Starting local LLM analysis')
      console.log('[LABELS] 🤖 filePath:', filePath)
      console.log('[LABELS] 🤖 fileContent length:', fileContent.length)

      // Check if content is sufficient for analysis
      if (fileContent.length < 50) {
        throw new Error('Insufficient content to generate ideas. Please ensure the document has meaningful content.')
      }

      // Simulate progress updates
      progressInterval = setInterval(() => {
        setProcessingProgress(prev => Math.min(prev + 10, 90))
      }, 300)

      // LOCAL LLM: Check for available local models
      console.log('[LABELS] 🤖 LOCAL-LLM: Checking for available local models')

      const providerStatus = await localModelService.getProviderStatus()
      console.log('[LABELS] 🤖 Provider Status:', {
        ollama: { connected: providerStatus.ollama.isConnected, models: providerStatus.ollama.models.length },
        lmstudio: { connected: providerStatus.lmstudio.isConnected, models: providerStatus.lmstudio.models.length }
      })

      // Check if any provider is connected
      const anyProviderConnected = providerStatus.ollama.isConnected || providerStatus.lmstudio.isConnected
      if (!anyProviderConnected) {
        throw new Error('No local model providers (Ollama/LM Studio) are available. Please start Ollama or LM Studio and ensure models are loaded.')
      }

      // Get all available models and select the best one
      const allModels = [...providerStatus.ollama.models, ...providerStatus.lmstudio.models]
      if (allModels.length === 0) {
        throw new Error('No local models found. Please ensure you have models installed in Ollama or LM Studio.')
      }

      // Prefer models with "llama" or "mistral" in the name, fallback to first available
      const preferredModel = allModels.find(m =>
        m.name.toLowerCase().includes('llama') ||
        m.name.toLowerCase().includes('mistral') ||
        m.name.toLowerCase().includes('qwen') ||
        m.name.toLowerCase().includes('gemma')
      ) || allModels[0]

      console.log('[LABELS] 🤖 LOCAL-LLM: Using model:', preferredModel.name)

      // Build enhanced prompt for key idea extraction
      const prompt = buildLabelExtractionPrompt(fileContent)
      const messages = [{ role: 'user', content: prompt }]

      console.log('[LABELS] 🤖 📡 Sending request to local model:', preferredModel.id)
      console.log('[LABELS] 🤖 📡 Prompt preview:', prompt.substring(0, 300) + '...')

      // Send to local model with JSON formatting preference
      const response = await localModelService.sendMessage(
        preferredModel.id,
        messages,
        undefined, // no streaming callback
        {
          temperature: 0.1,
          top_p: 0.9,
          max_tokens: 1024,
          force_json: true // Request JSON format if supported
        }
      )

      console.log('[LABELS] 🤖 📡 Raw response received:', response.substring(0, 500) + '...')

      // Parse the response to extract key ideas
      const keyIdeas = parseKeyIdeasFromLLMResponse(response, fileContent)

      // Clear progress and set to 100%
      if (progressInterval) {
        clearInterval(progressInterval)
        progressInterval = null
      }
      setProcessingProgress(100)

      console.log('[LABELS] 🤖 LOCAL-LLM: ✅ Analysis successful, extracted', keyIdeas.length, 'ideas')

      // Update label state with LLM results
      setLabelState(prev => ({
        ...prev,
        available_ideas: keyIdeas,
        selected_idea_ids: keyIdeas
          .filter(idea => idea.auto_selected)
          .map(idea => idea.id),
        processing_status: 'complete'
      }))

      // Notify parent components
      onLabelsChanged?.(keyIdeas)
      const processingTime = Date.now() - startTime

      // V02 COMPLIANT: Persist the analysis results using unified client
      await persistAnalysisResults(keyIdeas, processingTime, preferredModel.name)
      onProcessingComplete?.({
        success: true,
        file_path: filePath,
        processing_time_ms: processingTime,
        ideas_extracted: keyIdeas.length,
        entities_found: 0, // Not extracting entities in this simplified version
        human_connections: 0, // Not extracting connections in this simplified version
        confidence_score: keyIdeas.length > 0 ? 0.85 : 0,
        local_model_used: preferredModel.name
      })

      console.log('[LABELS] 🤖 LOCAL-LLM: Analysis completed successfully:', {
        filePath,
        ideasCount: keyIdeas.length,
        modelUsed: preferredModel.name,
        processingTime: processingTime + 'ms'
      })

    } catch (error) {
      console.error('[LABELS] ❌ processFileIntelligence failed:', error)
      
      // Clear progress interval safely
      if (progressInterval) {
        clearInterval(progressInterval)
        progressInterval = null
      }
      setProcessingProgress(0)
      
      // Determine if this is a user-actionable error or a system error
      let errorMessage: string
      let isRetryable: boolean = true
      
      if (error instanceof Error) {
        if (error.message.includes('Insufficient content')) {
          errorMessage = error.message
          isRetryable = false // Not retryable - user needs to add content
        } else if (error.message.includes('Kernel intelligence API not available')) {
          errorMessage = error.message
          isRetryable = false // Not retryable - system initialization issue
        } else if (error.message.includes('Kernel analysis not implemented')) {
          errorMessage = 'Analysis is not yet available in this build. Please update the app when the feature is enabled.'
          isRetryable = false
        } else if (error.message.includes('Kernel analysis failed')) {
          errorMessage = error.message
          isRetryable = true // Retryable - temporary kernel issue
        } else if (error.message.includes('network') || error.message.includes('timeout') || error.message.includes('rate limit')) {
          errorMessage = 'Network or service issue detected. Please try again in a moment.'
          isRetryable = true
        } else {
          errorMessage = 'Document analysis encountered an unexpected issue. Please try again.'
          isRetryable = true
        }
      } else {
        errorMessage = 'Document analysis failed. Please try again.'
        isRetryable = true
      }
      
      // Set error state for UI display
      setLabelState(prev => ({ 
        ...prev, 
        processing_status: 'error',
        error_message: errorMessage
      }))
      
      console.log('[LABELS] 🚨 Error details:', {
        error: error instanceof Error ? error.message : String(error),
        isRetryable,
        userMessage: errorMessage
      })
      
    } finally {
      setIsProcessing(false)
    }
  }

  /**
   * Toggle label selection
   */
  const toggleLabel = (ideaId: string) => {
    setLabelState(prev => {
      const isSelected = prev.selected_idea_ids.includes(ideaId)
      const newSelectedIds = isSelected
        ? prev.selected_idea_ids.filter(id => id !== ideaId)
        : [...prev.selected_idea_ids, ideaId]

      // Update user_confirmed status for the idea
      const updatedIdeas = prev.available_ideas.map(idea => 
        idea.id === ideaId 
          ? { ...idea, user_confirmed: !isSelected }
          : idea
      )

      // Always send ALL ideas to the overlay, not just selected ones
      console.log('[LABELS] 🏷️ Label toggled, sending', updatedIdeas.length, 'updated ideas to overlay')
      console.log('[LABELS] 🏷️ Updated ideas:', updatedIdeas.map(idea => ({
        id: idea.id,
        text: idea.text,
        relevance: idea.relevance_score,
        autoSelected: idea.auto_selected,
        userConfirmed: idea.user_confirmed
      })))
      onLabelsChanged?.(updatedIdeas)

      return {
        ...prev,
        selected_idea_ids: newSelectedIds,
        available_ideas: updatedIdeas
      }
    })
  }

  /**
   * Add custom user annotation
   */
  const addUserAnnotation = () => {
    if (!labelState.user_annotation.trim()) return

    const customIdea: KeyIdea = {
      id: `user_${Date.now()}`,
      text: labelState.user_annotation.trim(),
      relevance_score: 90, // User annotations get high relevance
      intent_types: ['topic'],
      weight: 0.90,
      auto_selected: false,
      user_confirmed: true,
      context: 'User-provided annotation',
      extracted_from: 'user_input'
    }

    setLabelState(prev => ({
      ...prev,
      available_ideas: [customIdea, ...prev.available_ideas],
      selected_idea_ids: [customIdea.id, ...prev.selected_idea_ids],
      user_annotation: ''
    }))

    setShowUserInput(false)

    // Notify parent with ALL ideas (including the new custom one)
    const allIdeas = [customIdea, ...labelState.available_ideas]
    console.log('[LABELS] 🏷️ Custom annotation added, sending', allIdeas.length, 'ideas to overlay')
    console.log('[LABELS] 🏷️ All ideas with custom:', allIdeas.map(idea => ({
      id: idea.id,
      text: idea.text,
      relevance: idea.relevance_score,
      autoSelected: idea.auto_selected,
      userConfirmed: idea.user_confirmed
    })))
    onLabelsChanged?.(allIdeas)
  }

  /**
   * Get label color based on relevance score and selection status
   */
  const getLabelColor = (idea: KeyIdea, isSelected: boolean) => {
    if (idea.extracted_from === 'user_input') {
      return isSelected 
        ? 'bg-purple-500/20 text-purple-400 border-purple-500/30'
        : 'bg-purple-500/10 text-purple-500 border-purple-500/20'
    }

    if (idea.auto_selected && !idea.user_confirmed) {
      // Auto-selected ideas get primary colors
      if (idea.relevance_score >= 90) {
        return isSelected 
          ? 'bg-primary/20 text-primary border-primary/30'
          : 'bg-primary/10 text-primary border-primary/20'
      } else if (idea.relevance_score >= 80) {
        return isSelected 
          ? 'bg-secondary/20 text-secondary border-secondary/30'
          : 'bg-secondary/10 text-secondary border-secondary/20'
      } else {
        return isSelected 
          ? 'bg-supplement2/20 text-supplement2 border-supplement2/30'
          : 'bg-supplement2/10 text-supplement2 border-supplement2/20'
      }
    }

    // Regular ideas
    return isSelected 
      ? 'bg-gray-600 text-gray-200 border-gray-500'
      : 'bg-gray-700 text-gray-300 border-gray-600'
  }

  /**
   * Get display ideas based on show_all_ideas state
   */
  const getDisplayIdeas = () => {
    console.log('[LABELS] 🎨 getDisplayIdeas called')
    console.log('[LABELS] 🎨 labelState.available_ideas.length:', labelState.available_ideas.length)
    console.log('[LABELS] 🎨 labelState.show_all_ideas:', labelState.show_all_ideas)
    console.log('[LABELS] 🎨 labelState.processing_status:', labelState.processing_status)

    if (labelState.show_all_ideas) {
      console.log('[LABELS] 🎨 Returning all ideas:', labelState.available_ideas.length)
      return labelState.available_ideas
    }

    // Show auto-selected + top 8 ideas
    const autoSelected = labelState.available_ideas.filter(idea => idea.auto_selected)
    const others = labelState.available_ideas
      .filter(idea => !idea.auto_selected)
      .slice(0, 8 - autoSelected.length)

    const result = [...autoSelected, ...others]
    console.log('[LABELS] 🎨 Returning filtered ideas:', {
      autoSelected: autoSelected.length,
      others: others.length,
      total: result.length
    })

    return result
  }

  console.log('[LABELS] 🎨 SmartLabelingInterface render called')
  console.log('[LABELS] 🎨 Current labelState:', {
    available_ideas_count: labelState.available_ideas.length,
    selected_idea_ids_count: labelState.selected_idea_ids.length,
    processing_status: labelState.processing_status,
    show_all_ideas: labelState.show_all_ideas
  })

  return (
    <div className="p-3 border-b border-tertiary/30">
      {/* Header */}
      <div className="flex items-center justify-between mb-2.5">
        <p className="text-xs text-gray-400 mb-2">
          Select any key ideas about this doc to enhance the AI context learning.
        </p>
        <div className="flex items-center gap-2.5">
          {labelState.processing_status === 'complete' && (
            <button
              onClick={() => processFileIntelligence()}
              className="text-sm text-primary hover:text-primary/80 transition-all duration-200 font-medium hover:bg-primary/10 px-2.5 py-1 rounded-md"
              title="Re-analyze document"
            >
              <FontAwesomeIcon icon={ICONS.arrowsRotate} className="mr-1.5" />
              Refresh
            </button>
          )}
          {labelState.processing_status !== 'complete' && (
            <button
              onClick={() => processFileIntelligence()}
              className="text-sm text-primary hover:text-primary/80 transition-all duration-200 font-medium hover:bg-primary/10 px-2.5 py-1 rounded-md"
              title="Analyze and generate intelligence"
            >
              <FontAwesomeIcon icon={ICONS.wandMagicSparkles} className="mr-1.5" />
              Analyze
            </button>
          )}
          {/* Organize: persist current ideas as a session (duplicate-safe) */}
          {labelState.available_ideas.length > 0 && (
            <button
              onClick={() => persistSession()}
              disabled={isOrganizing}
              className={`text-sm font-medium transition-all duration-200 px-2.5 py-1 rounded-md ${isOrganizing ? 'text-gray-500 bg-gray-700/30' : 'text-secondary hover:text-secondary/80 hover:bg-secondary/10'}`}
              title="Organize (save session)"
            >
              <FontAwesomeIcon icon={ICONS.folder} className="mr-1.5" />
              Organize
            </button>
          )}
        </div>
      </div>

      {/* Processing Status */}
      {isProcessing && (
        <div className="mb-2.5 p-2.5 bg-gray-900/60 rounded-lg border border-tertiary/40 shadow-lg">
          <div className="flex items-center gap-2.5 mb-2">
            <FontAwesomeIcon icon={ICONS.wandMagicSparkles} className="text-primary text-sm animate-pulse" />
            <span className="text-sm text-gray-300 font-medium">Analyzing document with AI...</span>
          </div>
          <div className="w-full bg-gray-700/50 rounded-full h-1.5">
            <div 
              className="bg-primary h-1.5 rounded-full transition-all duration-300 shadow-sm"
              style={{ width: `${processingProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Error Display */}
      {labelState.processing_status === 'error' && labelState.error_message && (
        <div className="mb-2.5 p-2.5 bg-red-900/20 border border-red-500/40 rounded-lg shadow-lg">
          <div className="flex items-center gap-2.5 mb-2">
            <FontAwesomeIcon icon={ICONS.exclamationTriangle} className="text-red-400 text-sm" />
            <span className="text-sm text-red-300 font-semibold">Analysis Failed</span>
          </div>
          <p className="text-sm text-red-200 leading-relaxed mb-2.5">
            {labelState.error_message}
          </p>
          <button
            onClick={() => processFileIntelligence()}
            className="px-2.5 py-1.5 bg-red-500/20 text-red-300 border border-red-500/40 rounded-lg text-sm hover:bg-red-500/30 transition-all duration-200 font-medium"
          >
            Try Again
          </button>
        </div>
      )}

      {/* Labels - Compact Multi-line Layout */}
      <div className="flex flex-wrap gap-1.5 mb-2.5">
        {getDisplayIdeas().map((idea) => {
          const isSelected = labelState.selected_idea_ids.includes(idea.id)
          
          // Truncate to max 3 words for display
          const words = idea.text.split(' ')
          const displayText = words.length > 3 ? words.slice(0, 3).join(' ') + '...' : idea.text
          
          return (
            <button
              key={idea.id}
              onClick={() => toggleLabel(idea.id)}
              className={`inline-flex items-center gap-1.5 px-2.5 py-1.5 text-xs rounded-full border font-medium transition-all duration-200 hover:scale-105 group relative ${getLabelColor(idea, isSelected)} max-w-full shadow-sm hover:shadow-md`}
              title={idea.text} // Simple hover showing full text
            >
              {/* Truncated text display */}
              <span className="opacity-90 leading-tight">
                {displayText}
              </span>
              <span className="text-xs opacity-75 font-normal flex-shrink-0">({idea.relevance_score}%)</span>
              {idea.auto_selected && !idea.user_confirmed && (
                <span className="ml-1 text-xs flex-shrink-0">⭐</span>
              )}
            </button>
          )
        })}

        {/* Show More/Less Toggle */}
        {labelState.available_ideas.length > 8 && (
          <button
            onClick={() => setLabelState(prev => ({ ...prev, show_all_ideas: !prev.show_all_ideas }))}
            className="px-2.5 py-1.5 border border-dashed border-gray-600 text-gray-400 text-xs rounded-full hover:border-gray-500 hover:bg-gray-700/30 transition-all duration-200 font-medium"
          >
            <FontAwesomeIcon icon={labelState.show_all_ideas ? ICONS.chevronUp : ICONS.chevronDown} className="mr-1.5" />
            {labelState.show_all_ideas ? 'Show Less' : `+${labelState.available_ideas.length - 8} More`}
          </button>
        )}

        {/* Add Custom Label */}
        <button
          onClick={() => setShowUserInput(!showUserInput)}
          className="px-2.5 py-1.5 border border-dashed border-gray-600 text-gray-400 text-xs rounded-full hover:border-gray-500 hover:bg-gray-700/30 transition-all duration-200 font-medium"
        >
          <FontAwesomeIcon icon={ICONS.plus} className="mr-1.5" />
          Add
        </button>
      </div>

      {/* Custom Input */}
      {showUserInput && (
        <div className="mt-2.5 p-2.5 bg-gray-900/60 rounded-lg border border-tertiary/40 shadow-lg">
          <div className="flex gap-2.5">
            <input
              type="text"
              value={labelState.user_annotation}
              onChange={(e) => setLabelState(prev => ({ ...prev, user_annotation: e.target.value }))}
              placeholder="Add custom label..."
              className="flex-1 bg-gray-800 border border-gray-600 rounded px-2 py-1 text-xs text-gray-200 placeholder-gray-500 focus:outline-none focus:border-primary/50"
              onKeyPress={(e) => e.key === 'Enter' && addUserAnnotation()}
            />
            <button
              onClick={addUserAnnotation}
              disabled={!labelState.user_annotation.trim()}
              className="px-2 py-1 bg-primary/20 text-primary border border-primary/30 rounded text-xs hover:bg-primary/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Add
            </button>
          </div>
        </div>
      )}

      {/* Selection Summary */}
      {labelState.selected_idea_ids.length > 0 && (
        <div className="mt-2 text-xs text-gray-400">
          {labelState.selected_idea_ids.length} label{labelState.selected_idea_ids.length !== 1 ? 's' : ''} selected
        </div>
      )}
    </div>
  )
}

/**
 * Build enhanced prompt for label extraction using local LLM
 */
function buildLabelExtractionPrompt(content: string): string {
  return `You are an expert document analyzer. Extract key ideas and labels from the following document content.

REQUIREMENTS:
- Extract at least 1 meaningful label/key idea (minimum requirement)
- Focus on the most important topics, themes, and concepts
- Each label should be 1-4 words maximum
- Labels should be specific and actionable
- Return results in valid JSON format

DOCUMENT CONTENT:
${content.substring(0, 4000)}${content.length > 4000 ? '\n\n[Content truncated for analysis...]' : ''}

Please analyze this content and return a JSON object with the following structure:
{
  "key_ideas": [
    {
      "id": "unique_id_1",
      "text": "Label Text",
      "relevance_score": 85,
      "intent_types": ["topic"],
      "weight": 1.0,
      "auto_selected": true,
      "user_confirmed": false,
      "context": "Brief context about why this label is relevant"
    }
  ]
}

Focus on extracting meaningful, actionable labels that represent the core concepts in this document. Ensure at least 1 label is extracted.`
}

/**
 * Parse key ideas from LLM response with robust fallback
 */
function parseKeyIdeasFromLLMResponse(response: string, originalContent: string): KeyIdea[] {
  console.log('[LABELS] 🤖 📝 Parsing LLM response...')

  try {
    // Try to extract JSON from the response
    let jsonStr = response.trim()

    // Look for JSON block in markdown format
    const jsonMatch = jsonStr.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/)
    if (jsonMatch) {
      jsonStr = jsonMatch[1]
    }

    // Try to find JSON object directly
    const jsonStart = jsonStr.indexOf('{')
    const jsonEnd = jsonStr.lastIndexOf('}')
    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      jsonStr = jsonStr.substring(jsonStart, jsonEnd + 1)
    }

    console.log('[LABELS] 🤖 📝 Attempting to parse JSON:', jsonStr.substring(0, 200) + '...')

    const parsed = JSON.parse(jsonStr)

    if (parsed.key_ideas && Array.isArray(parsed.key_ideas)) {
      const keyIdeas = parsed.key_ideas.map((idea: any, index: number) => ({
        id: idea.id || `llm_idea_${Date.now()}_${index}`,
        text: idea.text || `Idea ${index + 1}`,
        relevance_score: typeof idea.relevance_score === 'number' ? idea.relevance_score : 75,
        intent_types: Array.isArray(idea.intent_types) ? idea.intent_types : ['topic'],
        weight: typeof idea.weight === 'number' ? idea.weight : 1.0,
        auto_selected: idea.auto_selected !== false, // Default to true
        user_confirmed: idea.user_confirmed === true,
        context: idea.context || ''
      }))

      console.log('[LABELS] 🤖 📝 ✅ Successfully parsed', keyIdeas.length, 'ideas from LLM response')
      return keyIdeas
    }
  } catch (error) {
    console.log('[LABELS] 🤖 📝 ❌ JSON parsing failed:', error)
  }

  // Fallback: Extract labels using simple text analysis
  console.log('[LABELS] 🤖 📝 Using fallback text extraction...')
  return extractLabelsFromText(response, originalContent)
}

/**
 * Fallback method to extract labels from text when JSON parsing fails
 */
function extractLabelsFromText(response: string, originalContent: string): KeyIdea[] {
  const labels: KeyIdea[] = []

  // Look for bullet points, numbered lists, or key phrases in the response
  const lines = response.split('\n').map(line => line.trim()).filter(line => line.length > 0)

  for (const line of lines) {
    // Skip very long lines (likely not labels)
    if (line.length > 50) continue

    // Look for patterns like "- Label", "1. Label", "* Label"
    const labelMatch = line.match(/^[\-\*\d\.\)\s]*(.+)$/)
    if (labelMatch) {
      const labelText = labelMatch[1].trim()

      // Validate label (1-4 words, reasonable length)
      const words = labelText.split(/\s+/)
      if (words.length >= 1 && words.length <= 4 && labelText.length <= 30) {
        labels.push({
          id: `fallback_${Date.now()}_${labels.length}`,
          text: labelText,
          relevance_score: 70,
          intent_types: ['topic'],
          weight: 1.0,
          auto_selected: true,
          user_confirmed: false,
          context: 'Extracted from LLM response'
        })
      }
    }
  }

  // If no labels found, create a generic one based on content
  if (labels.length === 0) {
    const words = originalContent.split(/\s+/).slice(0, 20)
    const commonWords = words.filter(word =>
      word.length > 3 &&
      !/^(the|and|for|are|but|not|you|all|can|had|her|was|one|our|out|day|get|has|him|his|how|its|may|new|now|old|see|two|way|who|boy|did|man|men|put|say|she|too|use)$/i.test(word)
    )

    if (commonWords.length > 0) {
      labels.push({
        id: `generic_${Date.now()}`,
        text: commonWords[0].toLowerCase(),
        relevance_score: 60,
        intent_types: ['topic'],
        weight: 1.0,
        auto_selected: true,
        user_confirmed: false,
        context: 'Generated from document content'
      })
    } else {
      // Last resort: create a generic label
      labels.push({
        id: `document_${Date.now()}`,
        text: 'document',
        relevance_score: 50,
        intent_types: ['topic'],
        weight: 1.0,
        auto_selected: true,
        user_confirmed: false,
        context: 'Generic label for document'
      })
    }
  }

  console.log('[LABELS] 🤖 📝 ✅ Fallback extraction created', labels.length, 'labels')
  return labels
}
