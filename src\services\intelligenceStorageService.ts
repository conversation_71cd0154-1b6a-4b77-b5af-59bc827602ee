/**
 * Intelligence Storage Service
 * Manages JSON storage for file-level and vault-level intelligence data
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { 
  FileIntelligence, 
  VaultIntelligence, 
  KeyIdea, 
  WeightedEntity, 
  HumanConnection,
  ProcessingResult 
} from '../types/fileIntelligenceTypes'
// V02 COMPLIANT: Removed vaultPath dependency, using IPC path operations
// Note: crypto import removed for browser compatibility

export interface IntelligenceStorageOptions {
  vaultPath: string
  createDirectories?: boolean
}

class IntelligenceStorageService extends BaseService {
  private readonly CONTEXT_DIR = '.intelligence'  // V02 COMPLIANT: Use .intelligence instead of .context
  private readonly FILES_DIR = 'files'
  private readonly VAULT_INTELLIGENCE_FILE = 'vault_intelligence.json'

  /**
   * V02 COMPLIANT: Use path:join IPC for all path operations
   */
  private async joinPath(...parts: string[]): Promise<string> {
    if (parts.length === 0) return ''
    if (parts.length === 1) return parts[0]

    try {
      const result = await window.electronAPI.path.join(...parts)
      return result.success && result.path ? result.path : parts.join('/')
    } catch (error) {
      console.warn('[IntelligenceStorageService] Path join failed, using fallback:', error)
      return parts.join('/')
    }
  }

  /**
   * V02 COMPLIANT: Extract context path using simple parsing logic
   */
  private extractContextPath(filePath: string): string | null {
    if (!filePath || typeof filePath !== 'string') {
      return null
    }

    try {
      // Simple strategy: look for common subdirectories and extract parent
      const normalizedPath = filePath.replace(/\\/g, '/')
      const subDirs = ['documents', 'images', 'artifacts']

      for (const subDir of subDirs) {
        const token = `/${subDir}/`
        const idx = normalizedPath.lastIndexOf(token)
        if (idx !== -1) {
          const contextPath = normalizedPath.substring(0, idx)
          // Restore original separator style
          return filePath.includes('\\') ? contextPath.replace(/\//g, '\\') : contextPath
        }
      }

      return null
    } catch (error) {
      console.error('[IntelligenceStorageService] Error in extractContextPath:', error)
      return null
    }
  }

  constructor() {
    super({
      name: 'IntelligenceStorageService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    this.logger.info('Intelligence Storage Service initialized', 'doInitialize')
  }

  /**
   * Store file intelligence data
   */
  async storeFileIntelligence(
    filePath: string, 
    intelligence: FileIntelligence, 
    options: IntelligenceStorageOptions
  ): Promise<void> {
    return await this.executeOperationOrThrow(
      'storeFileIntelligence',
      async () => {
        console.log('[LABELS] 💾 storeFileIntelligence called')
        console.log('[LABELS] 💾 Storing filePath:', filePath)
        console.log('[LABELS] 💾 Intelligence key_ideas count:', intelligence.key_ideas.length)

        this.logger.info('Storing file intelligence', 'storeFileIntelligence', { filePath })

        // Generate file hash for unique identification
        console.log('[LABELS] 💾 STORAGE: About to generate hash for storage')
        console.log('[LABELS] 💾 STORAGE: Input filePath for storage:', filePath)
        const fileHash = this.generateFileHash(filePath)
        console.log('[LABELS] 💾 STORAGE: Generated hash for storage:', fileHash)

        // Resolve effective context path (prefer context derived from filePath)
        const derivedContext = this.extractContextPath(filePath)
        const effectiveContextPath = derivedContext || options.vaultPath

        // Create storage path under context folder, not vault root
        const contextDir = await this.getContextDirectory(effectiveContextPath)
        const filesDir = await this.getFilesDirectory(effectiveContextPath)
        const intelligenceFile = await this.joinPath(filesDir, `${fileHash}.json`)

        console.log('[LABELS] 💾 STORAGE: Storage paths calculated:')
        console.log('[LABELS] 💾 STORAGE:   vaultPath:', options.vaultPath)
        console.log('[LABELS] 💾 STORAGE:   effectiveContextPath:', effectiveContextPath)
        console.log('[LABELS] 💾 STORAGE:   filesDir:', filesDir)
        console.log('[LABELS] 💾 STORAGE:   intelligenceFile:', intelligenceFile)

        // 🚨 PATH RESOLUTION AUDIT LOG
        this.auditPathResolution('STORAGE', filePath, effectiveContextPath, filesDir)

        console.log('[LABELS] 💾 Storage paths:')
        console.log('[LABELS] 💾   contextDir:', contextDir)
        console.log('[LABELS] 💾   filesDir:', filesDir)
        console.log('[LABELS] 💾   intelligenceFile:', intelligenceFile)

        // Ensure directories exist
        if (options.createDirectories) {
          await this.ensureDirectoryExists(contextDir)
          await this.ensureDirectoryExists(filesDir)
        }

        // CORRUPTION PREVENTION: Validate intelligence before storage
        if (!intelligence.file_path || intelligence.file_path === 'undefined' || intelligence.file_path === 'null') {
          console.log('[LABELS] 🚨 CORRUPTION PREVENTION: Cannot store intelligence with invalid file_path:', intelligence.file_path)
          throw new Error(`Cannot store intelligence with invalid file_path: ${intelligence.file_path}`)
        }

        if (!intelligence.key_ideas || intelligence.key_ideas.length === 0) {
          console.log('[LABELS] 🚨 CORRUPTION PREVENTION: Cannot store intelligence with no key_ideas')
          throw new Error('Cannot store intelligence with no key_ideas')
        }

        // Prepare intelligence data with metadata
        const intelligenceData = {
          ...intelligence,
          storage_metadata: {
            file_path: filePath,
            file_hash: fileHash,
            stored_at: new Date().toISOString(),
            storage_version: '1.0'
          }
        }

        console.log('[LABELS] 💾 Intelligence data to store:')
        console.log('[LABELS] 💾   file_path in JSON:', intelligenceData.file_path)
        console.log('[LABELS] 💾   storage_metadata.file_path:', intelligenceData.storage_metadata.file_path)
        console.log('[LABELS] 💾   storage_metadata.file_hash:', intelligenceData.storage_metadata.file_hash)

        // Write to file
        console.log('[LABELS] 💾 Writing JSON file to:', intelligenceFile)
        console.log('[LABELS] 💾 Intelligence data size:', JSON.stringify(intelligenceData).length, 'characters')
        console.log('[LABELS] 💾 Key ideas count:', intelligenceData.key_ideas?.length || 0)

        try {
          await this.writeJsonFile(intelligenceFile, intelligenceData)
          console.log('[LABELS] 💾 ✅ JSON file written successfully')

          // Verify the file was actually created (with retry for file system sync)
          console.log('[LABELS] 💾 Verifying file was created...')
          let verifyResult = null
          let retryCount = 0
          const maxRetries = 3

          while (!verifyResult && retryCount < maxRetries) {
            if (retryCount > 0) {
              console.log(`[LABELS] 💾 Verification attempt ${retryCount + 1}/${maxRetries} (waiting for file system sync...)`)
              await new Promise(resolve => setTimeout(resolve, 100 * retryCount)) // Progressive delay
            }

            try {
              verifyResult = await this.readJsonFile(intelligenceFile)
            } catch (readError) {
              console.warn(`[LABELS] 💾 Verification attempt ${retryCount + 1} failed:`, readError)
            }

            retryCount++
          }

          if (verifyResult) {
            console.log('[LABELS] 💾 ✅ File verification successful - file exists and is readable')
            console.log('[LABELS] 💾 Verified file contains', verifyResult.key_ideas?.length || 0, 'ideas')
          } else {
            console.warn('[LABELS] 💾 ⚠️ File verification failed after', maxRetries, 'attempts')
            console.warn('[LABELS] 💾 File path:', intelligenceFile)
            console.warn('[LABELS] 💾 This is likely a file system sync timing issue')
            console.warn('[LABELS] 💾 The file was written successfully and should be available shortly')
            console.warn('[LABELS] 💾 This does not affect functionality - the intelligence data was stored correctly')
            // Don't throw error - the write was successful, verification is just a safety check
          }
        } catch (writeError) {
          console.error('[LABELS] 💾 ❌ Failed to write JSON file:', writeError)
          throw writeError
        }

        this.logger.info('File intelligence stored successfully', 'storeFileIntelligence', {
          filePath,
          intelligenceFile,
          ideasCount: intelligence.key_ideas.length,
          entitiesCount: intelligence.weighted_entities.length
        })
      },
      { filePath, vaultPath: options.vaultPath }
    )
  }

  /**
   * Retrieve file intelligence data
   */
  async getFileIntelligence(filePath: string, vaultPath: string): Promise<FileIntelligence | null> {
    console.log('[LABELS] 💾 intelligenceStorageService: getFileIntelligence called')
    console.log('[LABELS] 💾 Input filePath:', filePath)
    console.log('[LABELS] 💾 Input vaultPath:', vaultPath)

    // Resolve effective context path first (outside to use in telemetry as well)
    const derivedContext = this.extractContextPath(filePath)
    const effectiveContextPath = derivedContext || vaultPath

    console.log('[LABELS] 💾 derivedContext:', derivedContext)
    console.log('[LABELS] 💾 effectiveContextPath:', effectiveContextPath)

    return await this.executeOperationOrThrow(
      'getFileIntelligence',
      async () => {
        console.log('[LABELS] 💾 RETRIEVAL: About to generate hash for retrieval')
        console.log('[LABELS] 💾 RETRIEVAL: Input filePath for retrieval:', filePath)
        const fileHash = this.generateFileHash(filePath)
        console.log('[LABELS] 💾 RETRIEVAL: Generated hash for retrieval:', fileHash)

        const filesDir = await this.getFilesDirectory(effectiveContextPath)
        const intelligenceFile = await this.joinPath(filesDir, `${fileHash}.json`)

        console.log('[LABELS] 💾 Generated fileHash:', fileHash)
        console.log('[LABELS] 💾 filesDir:', filesDir)
        console.log('[LABELS] 💾 intelligenceFile path:', intelligenceFile)

        // 🚨 PATH RESOLUTION AUDIT LOG
        this.auditPathResolution('RETRIEVAL', filePath, effectiveContextPath, filesDir)

        this.logger.debug('Computed intelligence path', 'getFileIntelligence', {
          fileHash,
          intelligenceFile,
          vaultPath: effectiveContextPath
        })

        try {
          console.log('[LABELS] 💾 Attempting to read JSON file:', intelligenceFile)

          // STRICT: Only read the exact hash file, no fallbacks
          const data = await this.readJsonFile(intelligenceFile)

          console.log('[LABELS] 💾 JSON file read result:', data ? 'found' : 'null')

          if (data) {
            console.log('[LABELS] 💾 JSON content preview:', {
              file_path: data.file_path,
              key_ideas_count: data.key_ideas?.length || 0,
              created_at: data.created_at,
              updated_at: data.updated_at
            })
          }

          // STRICT VALIDATION: Only return data if it's valid and matches the file
          if (data && this.isValidFileIntelligence(data)) {
            // CORRUPTION DETECTION: Verify file_path matches exactly
            if (data.file_path !== filePath) {
              console.log('[LABELS] 🚨 CORRUPTION: file_path mismatch detected')
              console.log('[LABELS] 🚨 Expected:', filePath)
              console.log('[LABELS] 🚨 Found:', data.file_path)
              console.log('[LABELS] 🚨 Returning null to prevent data corruption')
              return null
            }

            console.log('[LABELS] 💾 ✅ Valid file intelligence found')
            console.log('[LABELS] 💾 JSON file_path matches input?', data.file_path === filePath)
            this.logger.info('File intelligence retrieved', 'getFileIntelligence', { filePath })
            return data as FileIntelligence
          }

          // STRICT: No legacy conversion, no fallbacks
          console.log('[LABELS] 💾 ℹ️ No valid intelligence found for file:', filePath)
          console.log('[LABELS] 💾 ℹ️ This is normal for files that haven\'t been analyzed yet')
          return null
        } catch (error) {
          // File doesn't exist or is invalid
          console.log('[LABELS] 💾 ❌ Error reading intelligence file:', error)
          this.logger.debug('File intelligence not found', 'getFileIntelligence', { filePath })
          return null
        }
      },
      { filePath, vaultPath: effectiveContextPath }
    )
  }

  /**
   * Debug helper: compute the JSON path for a given file without reading/writing
   */
  async debugIntelligencePath(filePath: string, vaultPath: string): Promise<{ fileHash: string; intelligenceFile: string; filesDir: string; contextDir: string }> {
    const fileHash = this.generateFileHash(filePath)
    const contextDir = await this.getContextDirectory(vaultPath)
    const filesDir = await this.getFilesDirectory(vaultPath)
    const intelligenceFile = await this.joinPath(filesDir, `${fileHash}.json`)
    return { fileHash, intelligenceFile, filesDir, contextDir }
  }

  /**
   * Debug helper: test hash generation with different path formats
   */
  debugHashGeneration(filePath: string): void {
    console.log('[LABELS] 🔍 DEBUG: Testing hash generation for:', filePath)

    // Test current algorithm
    const currentHash = this.generateFileHash(filePath)
    console.log('[LABELS] 🔍 Current algorithm hash:', currentHash)

    // Test with different normalizations
    const variants = [
      filePath,
      filePath.replace(/\\/g, '/'),
      filePath.replace(/\//g, '\\'),
      filePath.toLowerCase(),
      filePath.replace(/\\/g, '/').toLowerCase(),
      filePath.replace(/\//g, '\\').toLowerCase()
    ]

    variants.forEach((variant, index) => {
      const testHash = this.generateFileHashVariant(variant)
      console.log(`[LABELS] 🔍 Variant ${index} (${variant}):`, testHash)
    })
  }

  private generateFileHashVariant(filePath: string): string {
    const normalizedPath = filePath.replace(/\\/g, '/').toLowerCase()
    let hash = 0
    for (let i = 0; i < normalizedPath.length; i++) {
      const char = normalizedPath.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    const fileName = filePath.split(/[/\\]/).pop()?.replace(/\.[^.]*$/, '') || 'unknown'
    const hashSuffix = Math.abs(hash).toString(16).substring(0, 8)
    return `${fileName}_${hashSuffix}`
  }

  /**
   * 🚨 PATH RESOLUTION AUDIT - Log violations without breaking functionality
   */
  private auditPathResolution(operation: string, filePath: string, vaultPath: string, filesDir: string): void {
    console.log(`[🚨 PATH-AUDIT] ${operation}: Starting path resolution audit`)

    const violations: string[] = []
    const warnings: string[] = []

    // 1. Check for hardcoded paths
    if (filePath.includes('C:\\Users\\<USER>\\Documents\\Test20')) {
      violations.push('HARDCODED_USER_PATH: Contains hardcoded user path')
    }

    // 2. Check for inconsistent path separators
    const hasBackslash = filePath.includes('\\')
    const hasForwardSlash = filePath.includes('/')
    if (hasBackslash && hasForwardSlash) {
      warnings.push('MIXED_SEPARATORS: File path contains both \\ and / separators')
    }

    // 3. Check vault structure compliance
    const expectedSubdirs = ['documents', 'images', 'artifacts']
    const hasExpectedStructure = expectedSubdirs.some(subdir => filePath.includes(`/${subdir}/`) || filePath.includes(`\\${subdir}\\`))
    if (!hasExpectedStructure) {
      warnings.push('NON_STANDARD_STRUCTURE: File not in expected vault subdirectory (documents/images/artifacts)')
    }

    // 4. Check .context directory structure
    if (!filesDir.includes('.context/files') && !filesDir.includes('.context\\files')) {
      violations.push('INVALID_CONTEXT_STRUCTURE: Files directory does not follow .context/files pattern')
    }

    // 5. Check path normalization consistency
    const normalizedFilePath = filePath.replace(/\\/g, '/').toLowerCase()
    const normalizedVaultPath = vaultPath.replace(/\\/g, '/').toLowerCase()
    if (!normalizedFilePath.startsWith(normalizedVaultPath)) {
      violations.push('PATH_HIERARCHY_VIOLATION: File path does not start with vault path')
    }

    // 6. Check for legacy file system patterns
    if (filePath.includes('Documents/Chatlo') || filePath.includes('Documents\\Chatlo')) {
      violations.push('LEGACY_FILESYSTEM: Using deprecated Chatlo folder structure')
    }

    // 7. Check for plugin-incompatible patterns
    const pathSegments = filePath.split(/[/\\]/)
    if (pathSegments.some(segment => segment.includes(' ') && !segment.match(/^[a-zA-Z0-9\-_\s]+$/))) {
      warnings.push('PLUGIN_INCOMPATIBLE: Path contains special characters that may break plugins')
    }

    // Log results
    if (violations.length > 0) {
      console.error(`[🚨 PATH-AUDIT] ${operation}: VIOLATIONS FOUND:`)
      violations.forEach(violation => console.error(`[🚨 PATH-AUDIT]   ❌ ${violation}`))
      console.error(`[🚨 PATH-AUDIT]   📁 FilePath: ${filePath}`)
      console.error(`[🚨 PATH-AUDIT]   📁 VaultPath: ${vaultPath}`)
      console.error(`[🚨 PATH-AUDIT]   📁 FilesDir: ${filesDir}`)
    }

    if (warnings.length > 0) {
      console.warn(`[🚨 PATH-AUDIT] ${operation}: WARNINGS:`)
      warnings.forEach(warning => console.warn(`[🚨 PATH-AUDIT]   ⚠️ ${warning}`))
    }

    if (violations.length === 0 && warnings.length === 0) {
      console.log(`[🚨 PATH-AUDIT] ${operation}: ✅ Path resolution compliant`)
    }

    // Store audit results for future plugin compatibility analysis
    if (typeof window !== 'undefined' && !(window as any).pathAuditResults) {
      (window as any).pathAuditResults = []
    }
    if (typeof window !== 'undefined') {
      (window as any).pathAuditResults.push({
        timestamp: new Date().toISOString(),
        operation,
        filePath,
        vaultPath,
        filesDir,
        violations,
        warnings
      })

      // Add global audit summary function
      if (!(window as any).getPathAuditSummary) {
        (window as any).getPathAuditSummary = () => {
          console.log('[🚨 PATH-AUDIT] SUMMARY: Path Resolution Violations Report')
          console.log('[🚨 PATH-AUDIT] Total operations audited:', (window as any).pathAuditResults.length)

          const allViolations = (window as any).pathAuditResults.flatMap((r: any) => r.violations)
          const allWarnings = (window as any).pathAuditResults.flatMap((r: any) => r.warnings)

          console.log('[🚨 PATH-AUDIT] Total violations:', allViolations.length)
          console.log('[🚨 PATH-AUDIT] Total warnings:', allWarnings.length)

          // Group violations by type
          const violationCounts = {}
          allViolations.forEach(v => {
            const type = v.split(':')[0]
            violationCounts[type] = (violationCounts[type] || 0) + 1
          })

          console.log('[🚨 PATH-AUDIT] Violation breakdown:')
          Object.entries(violationCounts).forEach(([type, count]) => {
            console.log(`[🚨 PATH-AUDIT]   ${type}: ${count} occurrences`)
          })

          // Show recent violations
          const recentViolations = (window as any).pathAuditResults
            .filter((r: any) => r.violations.length > 0)
            .slice(-5)

          if (recentViolations.length > 0) {
            console.log('[🚨 PATH-AUDIT] Recent violations:')
            recentViolations.forEach((r: any) => {
              console.log(`[🚨 PATH-AUDIT]   ${r.operation}: ${r.violations.join(', ')}`)
              console.log(`[🚨 PATH-AUDIT]     Path: ${r.filePath}`)
            })
          }

          return {
            totalOperations: (window as any).pathAuditResults.length,
            totalViolations: allViolations.length,
            totalWarnings: allWarnings.length,
            violationBreakdown: violationCounts,
            recentViolations
          }
        }
      }
    }
  }

  /**
   * Store vault-level intelligence aggregation
   */
  async storeVaultIntelligence(
    vaultPath: string, 
    intelligence: VaultIntelligence
  ): Promise<void> {
    return await this.executeOperationOrThrow(
      'storeVaultIntelligence',
      async () => {
        this.logger.info('Storing vault intelligence', 'storeVaultIntelligence', { vaultPath })

        const contextDir = await this.getContextDirectory(vaultPath)
        const intelligenceFile = await this.joinPath(contextDir, this.VAULT_INTELLIGENCE_FILE)

        // Ensure directory exists
        await this.ensureDirectoryExists(contextDir)

        // Prepare intelligence data with metadata
        const intelligenceData = {
          ...intelligence,
          storage_metadata: {
            vault_path: vaultPath,
            stored_at: new Date().toISOString(),
            storage_version: '1.0'
          }
        }

        // Write to file
        await this.writeJsonFile(intelligenceFile, intelligenceData)

        this.logger.info('Vault intelligence stored successfully', 'storeVaultIntelligence', {
          vaultPath,
          totalFiles: intelligence.total_files_processed,
          totalIdeas: intelligence.aggregated_ideas.length
        })
      },
      { vaultPath }
    )
  }

  /**
   * Retrieve vault intelligence data
   */
  async getVaultIntelligence(vaultPath: string): Promise<VaultIntelligence | null> {
    return await this.executeOperationOrThrow(
      'getVaultIntelligence',
      async () => {
        const contextDir = await this.getContextDirectory(vaultPath)
        const intelligenceFile = await this.joinPath(contextDir, this.VAULT_INTELLIGENCE_FILE)

        try {
          const data = await this.readJsonFile(intelligenceFile)
          
          if (data && this.isValidFileIntelligence(data)) {
            this.logger.info('Vault intelligence retrieved', 'getVaultIntelligence', { vaultPath })
            return data as VaultIntelligence
          }

          return null
        } catch (error) {
          this.logger.debug('Vault intelligence not found', 'getVaultIntelligence', { vaultPath })
          return null
        }
      },
      { vaultPath }
    )
  }

  /**
   * Update vault intelligence incrementally
   */
  async updateVaultIntelligence(
    vaultPath: string, 
    newFileIntelligence: FileIntelligence,
    filePath: string
  ): Promise<void> {
    return await this.executeOperationOrThrow(
      'updateVaultIntelligence',
      async () => {
        // Get existing vault intelligence
        let vaultIntelligence = await this.getVaultIntelligence(vaultPath)

        if (!vaultIntelligence) {
          // Create new vault intelligence
          vaultIntelligence = {
            vault_path: vaultPath,
            total_files_processed: 0,
            last_updated: new Date().toISOString(),
            aggregated_ideas: [],
            top_entities: [],
            human_connections: [],
            processing_summary: {
              total_processing_time_ms: 0,
              average_confidence: 0,
              models_used: [],
              last_full_scan: new Date().toISOString()
            }
          }
        }

        // Update with new file data
        vaultIntelligence.total_files_processed += 1
        vaultIntelligence.last_updated = new Date().toISOString()

        // Merge key ideas (avoid duplicates)
        const existingIdeaTexts = new Set(vaultIntelligence.aggregated_ideas.map(idea => idea.text.toLowerCase()))
        const newIdeas = newFileIntelligence.key_ideas.filter(idea => 
          !existingIdeaTexts.has(idea.text.toLowerCase())
        )
        vaultIntelligence.aggregated_ideas.push(...newIdeas)

        // Sort by relevance and keep top 50
        vaultIntelligence.aggregated_ideas.sort((a, b) => b.relevance_score - a.relevance_score)
        vaultIntelligence.aggregated_ideas = vaultIntelligence.aggregated_ideas.slice(0, 50)

        // Merge entities (avoid duplicates)
        const existingEntityTexts = new Set(vaultIntelligence.top_entities.map(entity => entity.text.toLowerCase()))
        const newEntities = newFileIntelligence.weighted_entities.filter(entity => 
          !existingEntityTexts.has(entity.text.toLowerCase())
        )
        vaultIntelligence.top_entities.push(...newEntities)

        // Sort by weight and keep top 30
        vaultIntelligence.top_entities.sort((a, b) => b.weight - a.weight)
        vaultIntelligence.top_entities = vaultIntelligence.top_entities.slice(0, 30)

        // Merge human connections (avoid duplicates)
        const existingConnections = new Set(vaultIntelligence.human_connections.map(conn => 
          `${conn.name?.toLowerCase()}_${conn.email?.toLowerCase()}_${conn.company?.toLowerCase()}`
        ))
        const newConnections = newFileIntelligence.human_connections.filter(conn => {
          const key = `${conn.name?.toLowerCase()}_${conn.email?.toLowerCase()}_${conn.company?.toLowerCase()}`
          return !existingConnections.has(key)
        })
        vaultIntelligence.human_connections.push(...newConnections)

        // Sort by confidence and keep top 20
        vaultIntelligence.human_connections.sort((a, b) => b.connection_strength - a.connection_strength)
        vaultIntelligence.human_connections = vaultIntelligence.human_connections.slice(0, 20)

        // Update processing summary
        vaultIntelligence.processing_summary.total_processing_time_ms += 
          newFileIntelligence.analysis_metadata.processing_time_ms

        const modelUsed = newFileIntelligence.analysis_metadata.model_used
        if (modelUsed && !vaultIntelligence.processing_summary.models_used.includes(modelUsed)) {
          vaultIntelligence.processing_summary.models_used.push(modelUsed)
        }

        // Recalculate average confidence
        vaultIntelligence.processing_summary.average_confidence = 
          (vaultIntelligence.processing_summary.average_confidence * (vaultIntelligence.total_files_processed - 1) + 
           newFileIntelligence.processing_confidence) / vaultIntelligence.total_files_processed

        // Store updated vault intelligence
        await this.storeVaultIntelligence(vaultPath, vaultIntelligence)

        this.logger.info('Vault intelligence updated', 'updateVaultIntelligence', {
          vaultPath,
          filePath,
          totalFiles: vaultIntelligence.total_files_processed,
          totalIdeas: vaultIntelligence.aggregated_ideas.length
        })
      },
      { vaultPath, filePath }
    )
  }

  /**
   * Generate file hash for unique identification (deterministic)
   */
  private generateFileHash(filePath: string): string {
    console.log('[LABELS] 🔑 generateFileHash called with filePath:', filePath)

    // NORMALIZE the file path to ensure consistent hashing
    // Convert to forward slashes and lowercase for consistent hashing
    const normalizedPath = filePath.replace(/\\/g, '/').toLowerCase()
    console.log('[LABELS] 🔑 Normalized path for hashing:', normalizedPath)

    // Create deterministic hash based only on normalized file path (no timestamp)
    // This ensures the same file always gets the same hash
    let hash = 0
    for (let i = 0; i < normalizedPath.length; i++) {
      const char = normalizedPath.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }

    // Create a more readable filename from the original path
    const fileName = filePath.split(/[/\\]/).pop()?.replace(/\.[^.]*$/, '') || 'unknown'
    const hashSuffix = Math.abs(hash).toString(16).substring(0, 8)
    const result = `${fileName}_${hashSuffix}`

    console.log('[LABELS] 🔑 File hash generation:')
    console.log('[LABELS] 🔑   Original filePath:', filePath)
    console.log('[LABELS] 🔑   Normalized path:', normalizedPath)
    console.log('[LABELS] 🔑   Extracted fileName:', fileName)
    console.log('[LABELS] 🔑   Raw hash:', hash)
    console.log('[LABELS] 🔑   Hash suffix:', hashSuffix)
    console.log('[LABELS] 🔑   Final hash:', result)

    return result
  }

  /**
   * Get context directory path (V02 compliant)
   */
  private async getContextDirectory(vaultPath: string): Promise<string> {
    return await this.joinPath(vaultPath, this.CONTEXT_DIR)
  }

  /**
   * Get files directory path (V02 compliant)
   */
  private async getFilesDirectory(vaultPath: string): Promise<string> {
    return await this.joinPath(vaultPath, this.CONTEXT_DIR, this.FILES_DIR)
  }

  /**
   * Ensure directory exists
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      if (window.electronAPI?.vault?.createDirectory) {
        const result = await window.electronAPI.vault.createDirectory(dirPath)
        if (!result.success) {
          throw new Error(`Failed to create directory: ${result.error}`)
        }
      }
    } catch (error) {
      this.logger.error('Failed to ensure directory exists', 'ensureDirectoryExists', { dirPath, error })
      throw error
    }
  }

  /**
   * Write JSON data to file
   */
  private async writeJsonFile(filePath: string, data: any): Promise<void> {
    try {
      console.log('[LABELS] 💾 writeJsonFile: Starting write operation')
      console.log('[LABELS] 💾 writeJsonFile: Target path:', filePath)
      console.log('[LABELS] 💾 writeJsonFile: electronAPI available:', !!window.electronAPI?.vault?.writeFile)

      if (window.electronAPI?.vault?.writeFile) {
        // Use proper UTF-8 encoding for international characters
        const jsonContent = JSON.stringify(data, null, 2)
          .replace(/\\u[\dA-F]{4}/gi, (match) => {
            return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16))
          })

        console.log('[LABELS] 💾 writeJsonFile: JSON content length:', jsonContent.length)
        console.log('[LABELS] 💾 writeJsonFile: Calling electronAPI.vault.writeFile...')

        const result = await window.electronAPI.vault.writeFile(filePath, jsonContent)

        console.log('[LABELS] 💾 writeJsonFile: Write result:', result)

        if (!result.success) {
          console.error('[LABELS] 💾 writeJsonFile: Write failed with error:', result.error)
          throw new Error(`Failed to write file: ${result.error}`)
        }

        console.log('[LABELS] 💾 writeJsonFile: ✅ Write operation completed successfully')
        console.log('[LABELS] 💾 writeJsonFile: File has been written to disk and should be available')
      } else {
        console.error('[LABELS] 💾 writeJsonFile: electronAPI.vault.writeFile not available')
        throw new Error('electronAPI.vault.writeFile not available')
      }
    } catch (error) {
      console.error('[LABELS] 💾 writeJsonFile: Exception during write:', error)
      this.logger.error('Failed to write JSON file', 'writeJsonFile', { filePath, error })
      throw error
    }
  }

  /**
   * Read JSON file with error handling
   */
  private async readJsonFile(filePath: string): Promise<any> {
    try {
      if (!window.electronAPI?.vault?.readFile) {
        console.log('[LABELS] 💾 electronAPI.vault.readFile not available')
        return null
      }

      const result = await window.electronAPI.vault.readFile(filePath)
      if (!result.success) {
        console.log('[LABELS] 💾 Failed to read file:', result.error)
        return null
      }

      if (!result.content) {
        console.log('[LABELS] 💾 File read but no content')
        return null
      }

      try {
        const obj = JSON.parse(result.content)
        console.log('[LABELS] 💾 JSON parsed successfully')
        return obj
      } catch (parseError) {
        console.log('[LABELS] 💾 Failed to parse JSON:', parseError)
        return null
      }
    } catch (error) {
      console.log('[LABELS] 💾 Error reading JSON file:', error)
      return null
    }
  }

  /**
   * Check if a file exists
   */
  private async fileExists(filePath: string): Promise<boolean> {
    if (!window.electronAPI?.vault?.pathExists) {
      return false
    }
    
    try {
      const result = await window.electronAPI.vault.pathExists(filePath)
      return result.exists
    } catch (error) {
      console.log('[LABELS] 💾 Error checking if file exists:', error)
      return false
    }
  }

  /**
   * Clean up corrupted intelligence files
   */
  async cleanupCorruptedFiles(vaultPath: string): Promise<void> {
    return await this.executeOperationOrThrow(
      'cleanupCorruptedFiles',
      async () => {
        console.log('[LABELS] 🧹 cleanupCorruptedFiles called for vaultPath:', vaultPath)

        const contextDir = await this.getContextDirectory(vaultPath)
        const vaultIntelligenceFile = await this.joinPath(contextDir, 'vault_intelligence.json')

        console.log('[LABELS] 🧹 Cleanup paths:')
        console.log('[LABELS] 🧹   contextDir:', contextDir)
        console.log('[LABELS] 🧹   vaultIntelligenceFile:', vaultIntelligenceFile)

        // Clean up corrupted vault intelligence file
        if (await this.fileExists(vaultIntelligenceFile)) {
          try {
            const data = await this.readJsonFile(vaultIntelligenceFile)
            if (!this.isValidFileIntelligence(data)) {
              console.log('[LABELS] 🧹 Vault intelligence file is corrupted, removing...')
              if (window.electronAPI?.vault?.removeFile) {
                const result = await window.electronAPI.vault.removeFile(vaultIntelligenceFile)
                if (result.success) {
                  console.log('[LABELS] 🧹 Corrupted file removed successfully')
                } else {
                  console.log('[LABELS] 🧹 Failed to remove corrupted file:', result.error)
                }
              }
            }
          } catch (error) {
            console.log('[LABELS] 🧹 Error reading vault intelligence file, removing...')
            if (window.electronAPI?.vault?.removeFile) {
              const result = await window.electronAPI.vault.removeFile(vaultIntelligenceFile)
              if (result.success) {
                console.log('[LABELS] 🧹 Corrupted file removed successfully')
              } else {
                console.log('[LABELS] 🧹 Failed to remove corrupted file:', result.error)
              }
            }
          }
        }

        console.log('[LABELS] 🧹 Cleanup completed')
      },
      { vaultPath }
    )
  }

  /**
   * Clear all intelligence meta files in a vault (nuclear option for corrupted state)
   */
  async clearVaultIntelligenceState(vaultPath: string): Promise<void> {
    console.log('[LABELS] 🗑️ CLEAR-SERVICE: Starting clearVaultIntelligenceState for:', vaultPath)

    return await this.executeOperationOrThrow(
      'clearVaultIntelligenceState',
      async () => {
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: Getting context directory...')
        const contextDir = this.getContextDirectory(vaultPath)
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: Context directory:', contextDir)
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: CONTEXT_DIR constant:', this.CONTEXT_DIR)
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: Full context path calculation:', `${vaultPath}/${this.CONTEXT_DIR}`)

        // Delete vault intelligence file
        const vaultIntelligenceFile = await this.joinPath(contextDir, this.VAULT_INTELLIGENCE_FILE)
        console.log('🎯 [CLEAR-SERVICE] Target vault intelligence file:', vaultIntelligenceFile)

        console.log('🗑️ [CLEAR-SERVICE] Attempting to delete vault intelligence file...')
        if (window.electronAPI?.vault?.removeFile) {
          const result = await window.electronAPI.vault.removeFile(vaultIntelligenceFile)
          if (result.success) {
            console.log('✅ [CLEAR-SERVICE] Vault intelligence file removed successfully')
          } else {
            console.log('⚠️ [CLEAR-SERVICE] Failed to remove vault intelligence file:', result.error)
          }
        } else {
          console.log('❌ [CLEAR-SERVICE] electronAPI.vault.removeFile not available')
        }
        console.log('✅ [CLEAR-SERVICE] Vault intelligence file deletion completed')

        // Delete files directory if it exists
        const filesDir = await this.joinPath(contextDir, this.FILES_DIR)
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: Target files directory:', filesDir)
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: FILES_DIR constant:', this.FILES_DIR)
        console.log('[LABELS] 🗑️ CLEAR-SERVICE: Full files path calculation:', await this.joinPath(contextDir, this.FILES_DIR))

        try {
          console.log('🔧 [CLEAR-SERVICE] Checking electronAPI availability...')
          if (window.electronAPI?.vault?.removeDirectory) {
            console.log('✅ [CLEAR-SERVICE] electronAPI.vault.removeDirectory is available')
            console.log('🗑️ [CLEAR-SERVICE] Attempting to remove files directory...')

            const result = await window.electronAPI.vault.removeDirectory(filesDir)
            console.log('📊 [CLEAR-SERVICE] Remove directory result:', result)

            if (result.success) {
              console.log('✅ [CLEAR-SERVICE] Files directory removed successfully')
              this.logger.info('Cleared files intelligence directory', 'clearVaultIntelligenceState', { filesDir })
            } else {
              console.log('⚠️ [CLEAR-SERVICE] Remove directory returned success=false:', result)
            }
          } else {
            console.log('❌ [CLEAR-SERVICE] electronAPI.vault.removeDirectory is NOT available')
            console.log('🔍 [CLEAR-SERVICE] Available electronAPI methods:', {
              electronAPI: !!window.electronAPI,
              vault: !!window.electronAPI?.vault,
              removeDirectory: !!window.electronAPI?.vault?.removeDirectory,
              deleteDirectory: !!window.electronAPI?.vault?.deleteDirectory
            })
          }
        } catch (error) {
          console.error('💥 [CLEAR-SERVICE] Error during files directory cleanup:', error)
          this.logger.debug('Files directory cleanup skipped', 'clearVaultIntelligenceState', { filesDir, error })
        }

        console.log('🎉 [CLEAR-SERVICE] Vault intelligence state clearing completed')
        this.logger.info('Vault intelligence state cleared', 'clearVaultIntelligenceState', { vaultPath })
      },
      { vaultPath }
    )
  }

  /**
   * Validate file intelligence data structure
   */
  private isValidFileIntelligence(data: any): boolean {
    // Basic structure validation
    if (!data || 
        !Array.isArray(data.key_ideas) ||
        !Array.isArray(data.weighted_entities) ||
        !Array.isArray(data.human_connections) ||
        typeof data.processing_confidence !== 'number' ||
        !data.analysis_metadata) {
      return false
    }

    // CORRUPTION DETECTION: Check for invalid file_path
    if (!data.file_path || 
        data.file_path === 'undefined' || 
        data.file_path === 'null' ||
        typeof data.file_path !== 'string') {
      console.log('[LABELS] 🚨 CORRUPTION: isValidFileIntelligence detected invalid file_path:', data.file_path)
      return false
    }

    // CORRUPTION DETECTION: Check for completely empty content
    const hasValidContent = data.key_ideas.length > 0 || 
                           data.weighted_entities.length > 0 ||
                           data.human_connections.length > 0
    
    if (!hasValidContent) {
      console.log('[LABELS] 🚨 CORRUPTION: isValidFileIntelligence detected empty content')
      return false
    }

    return true
  }
}

export const intelligenceStorageService = new IntelligenceStorageService()
