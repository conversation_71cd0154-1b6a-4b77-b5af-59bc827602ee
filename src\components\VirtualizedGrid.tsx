import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'

interface VirtualizedGridProps<T> {
  items: T[]
  renderItem: (item: T, index: number) => React.ReactNode
  itemHeight: number
  containerHeight: number
  containerWidth: number
  columns: number
  gap: number
  overscan?: number
}

/**
 * Virtualized Grid Component
 * Optimized for rendering large numbers of items with minimal DOM nodes
 */
export function VirtualizedGrid<T>({
  items,
  renderItem,
  itemHeight,
  containerHeight,
  containerWidth,
  columns,
  gap,
  overscan = 2
}: VirtualizedGridProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)

  // Calculate grid dimensions
  const itemWidth = useMemo(() => {
    return (containerWidth - (gap * (columns - 1))) / columns
  }, [containerWidth, columns, gap])

  const rows = useMemo(() => {
    return Math.ceil(items.length / columns)
  }, [items.length, columns])

  const totalHeight = useMemo(() => {
    return rows * itemHeight + (rows - 1) * gap
  }, [rows, itemHeight, gap])

  // Calculate visible range
  const visibleStartRow = useMemo(() => {
    return Math.max(0, Math.floor(scrollTop / (itemHeight + gap)) - overscan)
  }, [scrollTop, itemHeight, gap, overscan])

  const visibleEndRow = useMemo(() => {
    const visibleRows = Math.ceil(containerHeight / (itemHeight + gap))
    return Math.min(rows - 1, visibleStartRow + visibleRows + overscan)
  }, [visibleStartRow, containerHeight, itemHeight, gap, rows, overscan])

  // Get visible items
  const visibleItems = useMemo(() => {
    const startIndex = visibleStartRow * columns
    const endIndex = Math.min(items.length, (visibleEndRow + 1) * columns)
    return items.slice(startIndex, endIndex).map((item, index) => ({
      item,
      globalIndex: startIndex + index,
      row: Math.floor((startIndex + index) / columns),
      col: (startIndex + index) % columns
    }))
  }, [items, visibleStartRow, visibleEndRow, columns])

  // Handle scroll
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])

  // Scroll to top when items change
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = 0
      setScrollTop(0)
    }
  }, [items.length])

  // Calculate transform for positioning
  const getItemTransform = useCallback((row: number, col: number) => {
    const x = col * (itemWidth + gap)
    const y = row * (itemHeight + gap)
    return `translate(${x}px, ${y}px)`
  }, [itemWidth, itemHeight, gap])

  if (items.length === 0) {
    return null
  }

  return (
    <div
      ref={containerRef}
      style={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative'
      }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        {visibleItems.map(({ item, globalIndex, row, col }) => (
          <div
            key={globalIndex}
            style={{
              position: 'absolute',
              width: itemWidth,
              height: itemHeight,
              transform: getItemTransform(row, col),
              transformOrigin: '0 0'
            }}
          >
            {renderItem(item, globalIndex)}
          </div>
        ))}
      </div>
    </div>
  )
}

export default VirtualizedGrid
