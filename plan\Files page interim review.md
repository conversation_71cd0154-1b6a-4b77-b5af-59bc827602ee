### Files Page — Interim Review (Explorer, Previews, Intelligence Overlay)

| Category | Current State | Evidence / Story (Experience, Services/Components, APIs) | Maturity (0–5) | Improvements |
|---|---|---|---:|---|
| Explorer & Navigation | Files page provides explorer and master modes; vault-focused routing expected | Story: Users arrive with a vault focus from Home; explorer lists files/folders; master mode surfaces `master.md` canvas (per plan). Hooks: `useNavigation`. | 3.5 | Ensure route params capture vault and path; add breadcrumb and quick jump between Explorer/Master modes. Persist expanded tree across sessions. |
| File Previewers | Robust preview components across types (pdf, markdown, text, image, code) | Story: `FilePageOverlay` renders PDFs (local pdf.js worker), images, markdown, code; `FilePreviewPane`, `DocumentViewer`, `ImagePreview` also used across the app. | 4.5 | Add thumbnail generation and caching; graceful fallbacks; show file metadata panel (size, type, last modified). |
| Intelligence Overlay | Smart labeling and intelligence state integrated into overlay | Story: Overlay uses `SmartLabelingInterface` as source of truth for intelligence; loads intelligence via storage adapter and context extraction; aligns with Just-in-Time triggers. | 4.0 | Add per-file “Analyze now” CTA and status; show top key ideas with weights; link to `.intelligence` data. |
| Path Resolution & Context | Uses `extractContextPath` and storage adapter; path normalization improving | Story: Overlay imports `extractContextPath` from `src/utils/vaultPath`; storage adapter translates legacy paths to relative context; avoids hardcoded paths. | 3.5 | Complete migration of any remaining string-based joining; introduce a shared renderer util for path joins and a main-process canonicalizer IPC. |
| Eventing & Realtime | Viewer/overlay state internal; no file watcher updates yet | Story: Updates on explicit user actions; no chokidar-backed updates in UI. | 3.0 | Add file change events (added/removed/changed) to refresh listing and overlay when underlying files update. |
| Editing & Actions | Read-only or limited editing (text area toggle placeholder) | Story: `isEditMode` intentionally disabled; saving flows not implemented; downloads available. | 3.0 | Add safe write path via vault APIs; confirm dialogs; diff preview for markdown; undo/redo buffer. |
| Accessibility & UX | Strong visual layout; zoom controls and keyboard likely basic | Story: PDF zoom, basic controls; icons from centralized registry. | 3.5 | Keyboard navigation in explorer; ARIA roles/labels; remember zoom per file type; reduced motion. |
| Testing Readiness | View-model + services separations enable tests | Story: file viewer services (`fileViewerService`, `PDFViewerService`), storage adapter, and context utils can be unit-tested. | 3.5 | Add tests for context extraction, overlay state transitions, and renderer-worker integration for pdf.js.

### Intelligence Overlay — Case Review vs System Architecture
- Cross-check basis: “System Architecture Interim Review” notes gaps in Path Resolution, Storage Abstraction, Eventing, and Vault IPC endpoints. Repeated symptoms in Files page match those gaps: “write claims success but files not created,” brittle Markdown→JSON transforms, and labels not rendering in `FilePageOverlay.tsx` sidebar.

- Root Causes (most likely):
  - Path/Storage inconsistency: mixed separators and ad-hoc joins from renderer cause main-process writes to fail silently. Architecture doc flags multiple path builders and legacy hardcoded roots. Overlay currently calls `storageServiceAdapter.storeFileIntelligence(state.filePath, updated, { vaultPath: extractContextPath(state.filePath), createDirectories: true })`, but without a canonical main-process path join, directory creation/writes can no-op on Windows.
  - Missing/partial Vault IPC endpoints: the gap analysis shows absent `vault:*` endpoints (e.g., `writeFile`, `ensureDir`, `readFile`). Falling back to generic `files.getFileContent` for reads and ad-hoc writes reduces reliability to intelligence directories.
  - Markdown→JSON extraction drift: earlier logic transformed LLM Markdown to JSON with heuristics. Although Markdown format is now stabilized, a strict, single parser isn’t consistently applied before persisting JSON, leading to malformed or empty intelligence objects.
  - Overlay load path removed: `FilePageOverlay.tsx` removed the legacy loader in favor of a producer-only `SmartLabelingInterface`. When opening a file, persisted intelligence is not read first; labels rely on live session only, so the sidebar appears empty despite prior writes.

- Evidence from code and docs:
  - Path issues: `.context/audit/comprehensive_path_resolution_audit.md`; legacy hardcoded root warning in `electron/fileSystem.ts`; multiple `extractContextPath` implementations mentioned in audit docs; some UI paths built with `'/'` concatenation.
  - IPC gaps: `IPCHandler_Gap_Analysis.md` lists missing `vault:*` endpoints required to create directories and write `.intelligence` files.
  - Overlay behavior: `FilePageOverlay.tsx` uses `extractContextPath` and `storageServiceAdapter.storeFileIntelligence(...)`, but has removed “load existing intelligence” logic; sidebar renders from in-memory state only.

- Correct Storage Target (align with architecture):
  - Use one canonical location for per-file intelligence. Align to the chosen standard (project currently references both `.intelligence/documents/<hash>/...` and `.context/files/<hash>.json`). Pick one and enforce consistently. If following current memory/practice, use `<context>/.context/files/<hash>.json` for single-file JSON, or the more structured `<vault>/.intelligence/documents/<hash>/sessions/session_<ts>.json` when session history is needed. Update adapter and readers accordingly.

- Action Plan (concrete):
  1) Canonical Path Resolver
     - Implement a main-process `PathResolver` used by all vault file ops (join, normalize, ensureDir). Expose via `vault:pathResolve`, `vault:ensureDir`, `vault:writeFile`, `vault:readFile` IPC. Renderer stops string concatenation; passes semantic inputs (vaultId, relativePath).
  2) Complete Vault IPC set
     - Add `vault:writeFile`, `vault:readFile`, `vault:ensureDir`, `vault:pathExists` with strict validation and path normalization. Update `storageServiceAdapter` to use these exclusively for `.intelligence` writes/reads.
  3) Stabilize Markdown→JSON parser
     - Define a single parser: prefer fenced ```json blocks; fallback to front-matter; final fallback: parse Key Ideas bullet list into the known schema. Centralize in a service (e.g., `intelligenceParsingService`) and use it in `SmartLabelingInterface` before persistence.
  4) Re-introduce persisted load on open
     - On overlay open: attempt to load existing intelligence via `storageServiceAdapter.readFileIntelligence(filePath)` and render labels immediately. Keep `SmartLabelingInterface` as the producer; emit an `intelligence:updated` event that the overlay listens to for live refresh.
  5) Tests
     - Path normalization (Windows separators), write–read roundtrip to `.intelligence`, Markdown→JSON parser unit tests (happy/edge), overlay load/refresh integration tests.

- Expected Outcomes
  - Intelligence JSON consistently written to the correct folder with directories auto-created in main process.
  - Overlay sidebar shows persisted labels immediately on open; live updates appear after processing; no more “write success but nothing there.”
  - No duplicate path logic; renderer uses semantic vault operations only.

### Overall Alignment
- Files page is close to the target experience: strong previews and an intelligent overlay, with ongoing work on path consistency and realtime updates. Explorer/master routing and eventing are the main opportunities.

### High-Impact Improvements
- Route param standardization (`/files/:vaultId?path=...&mode=explorer|master`).
- FS event bus integration to auto-refresh listing and overlay.
- Intelligence status indicators and analyze-now actions per file.
- Thumbnail cache and metadata side panel for quick scanning.
