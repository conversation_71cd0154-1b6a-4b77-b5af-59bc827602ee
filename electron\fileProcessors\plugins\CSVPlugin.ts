/**
 * CSV File Processing Plugin
 * Core plugin for processing CSV/TSV-like files without external dependencies
 */

import * as fs from 'fs'
import * as path from 'path'
import { FileProcessorPlugin, ProcessedFileContent } from '../types'

export default class CSVPlugin implements FileProcessorPlugin {
  name = 'CSVPlugin'
  version = '1.0.0'
  description = 'Core plugin for processing CSV files'
  author = 'ChatLo Team'
  optional = false

  supportedTypes = ['text']
  supportedExtensions = ['.csv']

  canProcess(filePath: string, fileType: string): boolean {
    const extension = path.extname(filePath).toLowerCase()
    return this.supportedExtensions.includes(extension)
  }

  async process(filePath: string): Promise<ProcessedFileContent> {
    try {
      const stats = await fs.promises.stat(filePath)
      const content = await fs.promises.readFile(filePath, 'utf8')

      const { delimiter, rows, cols, sample, headerDetected } = this.analyzeCSV(content)

      const previewText = this.renderPreview(sample)

      return {
        text: previewText,
        metadata: {
          fileSize: stats.size,
          lastModified: stats.mtime,
          extension: path.extname(filePath),
          processor: this.name,
          delimiter,
          rows,
          cols,
          headerDetected
        }
      }
    } catch (error: any) {
      return {
        error: `Failed to process CSV file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  private analyzeCSV(content: string) {
    const lines = content.split(/\r?\n/).filter(l => l.length > 0)
    const sampleLines = lines.slice(0, 50)

    // Detect delimiter by comparing counts
    const candidates = [',', '\t', ';', '|']
    let bestDelimiter = ','
    let bestVariance = Number.MAX_VALUE

    for (const d of candidates) {
      const counts = sampleLines.map(l => this.splitSafe(l, d).length)
      if (counts.length === 0) continue
      const avg = counts.reduce((a, b) => a + b, 0) / counts.length
      const variance = counts.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / counts.length
      if (variance < bestVariance) {
        bestVariance = variance
        bestDelimiter = d
      }
    }

    const parsed = sampleLines.map(l => this.splitSafe(l, bestDelimiter))
    const cols = parsed.length > 0 ? Math.max(...parsed.map(r => r.length)) : 0
    const headerDetected = parsed.length > 1 && this.looksLikeHeader(parsed[0], parsed[1])

    return {
      delimiter: bestDelimiter === '\t' ? 'tab' : bestDelimiter,
      rows: lines.length,
      cols,
      sample: parsed.slice(0, 10),
      headerDetected
    }
  }

  private splitSafe(line: string, delimiter: string): string[] {
    // Minimal CSV parser: handle quotes and escaped quotes
    const result: string[] = []
    let current = ''
    let inQuotes = false
    for (let i = 0; i < line.length; i++) {
      const ch = line[i]
      if (ch === '"') {
        if (inQuotes && line[i + 1] === '"') {
          current += '"'
          i++
        } else {
          inQuotes = !inQuotes
        }
      } else if (ch === delimiter && !inQuotes) {
        result.push(current)
        current = ''
      } else {
        current += ch
      }
    }
    result.push(current)
    return result.map(v => v.trim())
  }

  private looksLikeHeader(row1: string[], row2: string[]): boolean {
    const alpha1 = row1.filter(v => /[A-Za-z]/.test(v)).length
    const alpha2 = row2.filter(v => /[A-Za-z]/.test(v)).length
    return alpha1 >= alpha2
  }

  private renderPreview(rows: string[][]): string {
    if (rows.length === 0) return 'CSV: empty preview'
    const maxCols = Math.max(...rows.map(r => r.length))
    const truncated = rows.map(r => r.map(c => (c.length > 80 ? c.slice(0, 77) + '...' : c)))
    const lines = truncated.map(r => {
      const padded = [...r]
      while (padded.length < maxCols) padded.push('')
      return padded.join('\t')
    })
    return lines.join('\n')
  }
}


