import { APIRegistry } from '../api/APIRegistry'
import { UniversalPluginManager } from '../plugins/PluginManager'
import { PluginCapability } from '../plugins/types'

export class PluginCoreService {
  private manager: UniversalPluginManager

  constructor(apiRegistry: APIRegistry) {
    this.manager = new UniversalPluginManager(apiRegistry)
  }

  addPluginDirectory(directory: string): void {
    this.manager.addPluginDirectory(directory)
  }

  async discover(): Promise<any[]> {
    return this.manager.discoverPlugins()
  }

  async load(manifest: any): Promise<void> {
    await this.manager.loadPlugin(manifest)
  }

  getAllPluginsInfo(): Array<{id: string, name: string, version: string, state: string, capabilities: string[]}> {
    return this.manager.getAllPluginsInfo()
  }

  setPluginEnabled(pluginId: string, enabled: boolean): void {
    this.manager.setPluginEnabled(pluginId, enabled)
  }

  getPluginConfig(pluginId: string): Record<string, any> | undefined {
    return this.manager.getPluginConfig(pluginId)
  }

  updatePluginConfig(pluginId: string, config: Record<string, any>): void {
    this.manager.updatePluginConfig(pluginId, config)
  }

  getPluginNamespace(pluginId: string) {
    return this.manager.getPluginNamespace(pluginId)
  }

  getAllPluginNamespaces() {
    return this.manager.getAllPluginNamespaces()
  }

  getCapabilities(): string[] {
    return Object.values(PluginCapability)
  }
}
