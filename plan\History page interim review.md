### History Page — Interim Review (Conversation and File History, Retrieval & Navigation)

| Category | Current State | Evidence / Story (Experience, Services/Components, APIs) | Maturity (0–5) | Improvements |
|---|---|---|---:|---|
| Conversation Listing & Filters | Likely lists conversations with recency; DB indices support fast fetch | Story: User opens History to browse conversations; DB schema includes indices on `messages` and `conversations` (`electron/database.ts`), enabling efficient queries; navigation opens Chat with selected conversation. | 3.5 | Add vault/context filter, model filter, and pinned-only view. Provide full-text search across messages (FTS5). |
| Message Preview & Metadata | Basic title/preview expected; last activity surfaced | Story: Titles and updated timestamps exist on `conversations`; can render last message snippet. | 3.5 | Show entities/topics (from `messages.entities/topics`) as chips; quick pin/unpin; show extraction confidence. |
| File/Artifact History | Artifacts have indices and types; can be listed per conversation | Story: `artifacts` table with indices enables a Files/Artifacts tab inside History; clicking opens relevant viewer. | 3.5 | Add artifact filters (code/markdown/mermaid/html/json); previews with thumbnails; open in overlay. |
| Vault-Centric Navigation | Should deep-link back to Chat/Files with vault focus | Story: History items carry vault or context id (via conversation metadata or mapping); `useNavigation` routes accordingly. | 3.0 | Persist and display vault on each conversation; enable filtering by vault and jump-to-files for that vault. |
| Eventing & Live Updates | Updates when new messages arrive via store/DB | Story: On new messages, History refreshes; currently no global event bus. | 3.0 | Subscribe to a unified event bus for conversation created/updated, artifact added, pin changed. |
| Privacy & Local-First | Purely local retrieval | Story: Reads via IPC from SQLite; no remote calls. | 5.0 | Add privacy badges and filters (private vs shared contexts if applicable). |
| Accessibility & UX | Sort/filters are likely basic; infinite scroll/pagination TBD | Story: Standard list UX. | 3.0 | Add keyboard navigation, ARIA labels, and paginated/infinite lists for performance. |
| Testing Readiness | Query seams testable; indices exist | Story: Integration tests can validate ordering and filter logic. | 3.0 | Add tests for filters (vault/model/pinned), pagination, and artifact joins.

### Overall Alignment
- History benefits from strong DB schema/indices and should integrate tightly with vault-centric routing. Main opportunities are richer filtering (vault/model/pins), FTS search, event-driven updates, and artifact views.

### High-Impact Improvements
- Add full-text search (FTS5) and facet filters (vault, model, pinned, date range).
- Display entities/topics as chips and pinned markers; one-click pin/unpin.
- Event bus for live updates on conversation/artifact changes.
- Keyboard navigation and accessible list semantics; performance pagination.
